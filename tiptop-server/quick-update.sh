#!/bin/bash

# TipTop Quick Update Script
# This script performs a zero-downtime rolling update of the TipTop services

# Function to handle errors
handle_error() {
    echo "❌ Error occurred in quick update script at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Function to show usage
show_usage() {
    echo "Usage: $0 <GCP_PROJECT_ID> [UPDATE_TYPE]"
    echo ""
    echo "This script performs a zero-downtime rolling update of TipTop services."
    echo "It builds new images and updates the running deployments without downtime."
    echo ""
    echo "Arguments:"
    echo "  GCP_PROJECT_ID  - Your GCP project ID"
    echo "  UPDATE_TYPE     - Optional. What to update:"
    echo "                    'server' - Update only tiptop-server (cloud function)"
    echo "                    'websocket' - Update only websocket-server"
    echo "                    'both' or empty - Update both services (default)"
    echo ""
    echo "Examples:"
    echo "  $0 my-gcp-project-123                    # Update both services"
    echo "  $0 my-gcp-project-123 server             # Update only tiptop-server"
    echo "  $0 my-gcp-project-123 websocket          # Update only websocket-server"
    echo "  $0 my-gcp-project-123 both               # Update both services"
    exit 1
}

# Check arguments
if [ -z "$1" ]; then
    echo "❌ Error: GCP Project ID is required"
    show_usage
fi

GCP_PROJECT_ID=$1
UPDATE_TYPE=${2:-both}  # Default to 'both' if not specified

# Validate update type
case "$UPDATE_TYPE" in
    server|websocket|both)
        ;;
    *)
        echo "❌ Error: Invalid update type '$UPDATE_TYPE'"
        echo "Valid options: server, websocket, both"
        show_usage
        ;;
esac

echo "🚀 TipTop Quick Rolling Update"
echo "📋 Project ID: $GCP_PROJECT_ID"
echo "🎯 Update Type: $UPDATE_TYPE"
echo "🔄 This will perform a zero-downtime update"
echo ""

# Check if required tools are installed
echo "🔍 Checking required tools..."
command -v gcloud >/dev/null 2>&1 || { echo "❌ gcloud CLI is required but not installed."; exit 1; }
command -v kubectl >/dev/null 2>&1 || { echo "❌ kubectl is required but not installed."; exit 1; }
command -v docker >/dev/null 2>&1 || { echo "❌ docker is required but not installed."; exit 1; }

# Set GCP project
echo "🔧 Setting GCP project..."
gcloud config set project $GCP_PROJECT_ID

# Configure Docker for GCR
echo "🐳 Configuring Docker for Google Container Registry..."
gcloud auth configure-docker

# Check current deployment status based on update type
echo "📊 Checking current deployment status..."

if [ "$UPDATE_TYPE" = "server" ] || [ "$UPDATE_TYPE" = "both" ]; then
    if ! kubectl get deployment cloud-function-tiptop-deployment -n tiptop >/dev/null 2>&1; then
        echo "❌ Cloud Function deployment not found!"
        echo "💡 You may need to run a full deployment first:"
        echo "   ./deploy-to-gcp.sh $GCP_PROJECT_ID"
        exit 1
    fi
    echo "✅ Found Cloud Function deployment"
fi

if [ "$UPDATE_TYPE" = "websocket" ] || [ "$UPDATE_TYPE" = "both" ]; then
    if ! kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
        echo "❌ WebSocket deployment not found!"
        echo "💡 You may need to run a full deployment first:"
        echo "   ./deploy-to-gcp.sh $GCP_PROJECT_ID"
        exit 1
    fi
    echo "✅ Found WebSocket deployment"
fi

echo ""

# Generate timestamp for image tags
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Build and push Cloud Function image (if needed)
if [ "$UPDATE_TYPE" = "server" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "🏗️  Building Cloud Function image with tag: $TIMESTAMP..."
    cd cloud-function-tiptop
    docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP .
    docker tag gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest

    echo "📤 Pushing Cloud Function image..."
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:latest
    cd ..
    echo "✅ Cloud Function image built and pushed"
fi

# Build and push WebSocket server image (if needed)
if [ "$UPDATE_TYPE" = "websocket" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "🏗️  Building WebSocket server image with tag: $TIMESTAMP..."
    cd websocket-server
    docker build -t gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP .
    docker tag gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest

    echo "📤 Pushing WebSocket server image..."
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP
    docker push gcr.io/$GCP_PROJECT_ID/tiptop-websocket:latest
    cd ..
    echo "✅ WebSocket server image built and pushed"
fi

# Perform rolling updates based on update type
echo ""
echo "🔄 Starting rolling updates..."

# Update Cloud Function (if needed)
if [ "$UPDATE_TYPE" = "server" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "🔄 Performing rolling update for Cloud Function..."
    kubectl set image deployment/cloud-function-tiptop-deployment \
        cloud-function-tiptop=gcr.io/$GCP_PROJECT_ID/tiptop-cloud-function:$TIMESTAMP -n tiptop

    echo "📊 Monitoring Cloud Function rollout..."
    kubectl rollout status deployment/cloud-function-tiptop-deployment -n tiptop --timeout=300s
    echo "✅ Cloud Function update completed"
fi

# Update WebSocket server (if needed)
if [ "$UPDATE_TYPE" = "websocket" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "🔄 Performing rolling update for WebSocket server..."
    kubectl set image deployment/tiptop-websocket \
        tiptop-websocket=gcr.io/$GCP_PROJECT_ID/tiptop-websocket:$TIMESTAMP -n tiptop

    echo "📊 Monitoring WebSocket server rollout..."
    kubectl rollout status deployment/tiptop-websocket -n tiptop --timeout=300s
    echo "✅ WebSocket server update completed"
fi

echo ""
echo "✅ Rolling update completed successfully!"
echo "🎯 Updated: $UPDATE_TYPE"
echo ""
echo "📊 Current Status:"
kubectl get pods -n tiptop
echo ""
echo "🔍 Verify the update:"

if [ "$UPDATE_TYPE" = "server" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "  - API: curl https://tiptop.qubitrhythm.com/"
    echo "  - Cloud Function logs: kubectl logs deployment/cloud-function-tiptop-deployment -n tiptop"
fi

if [ "$UPDATE_TYPE" = "websocket" ] || [ "$UPDATE_TYPE" = "both" ]; then
    echo "  - WebSocket logs: kubectl logs deployment/tiptop-websocket -n tiptop"
    echo "  - Test WebSocket: Open browser console and test connection"
fi

echo ""
echo "🎯 The update was performed with zero downtime!"
echo "Users should not have experienced any service interruption."

# Show what was updated
case "$UPDATE_TYPE" in
    server)
        echo "📦 Updated: TipTop Cloud Function (API server) only"
        ;;
    websocket)
        echo "📦 Updated: WebSocket server only"
        echo "💡 This includes the chat message ordering improvements"
        ;;
    both)
        echo "📦 Updated: Both TipTop Cloud Function and WebSocket server"
        echo "💡 This includes all latest improvements"
        ;;
esac
