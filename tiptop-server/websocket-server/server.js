require('dotenv').config();
const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const url = require('url');
const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

// Determine if we're in test mode from environment variables
const isTestMode = process.env.TIPTOP_TEST_MODE === 'true' || process.env.NODE_ENV === 'development';
console.log(`WebSocket server running in ${isTestMode ? 'TEST' : 'PRODUCTION'} mode`);

// Create Express app
const app = express();
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Set up heartbeat interval to detect dead connections
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const heartbeatInterval = setInterval(() => {
  console.log('Sending heartbeat to all clients');
  let liveCount = 0;
  let deadCount = 0;

  wss.clients.forEach(client => {
    if (client.isAlive === false) {
      // Client didn't respond to the previous ping, terminate it
      console.log('Terminating dead connection');
      deadCount++;
      client.terminate();
      return;
    }

    // Client is alive, reset the flag and send a ping
    client.isAlive = false;
    liveCount++;
    client.ping(() => {});
  });

  console.log(`Heartbeat complete: ${liveCount} live connections, ${deadCount} dead connections terminated`);
}, HEARTBEAT_INTERVAL);

// PostgreSQL connection with optimized pooling
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'postgres',
  database: process.env.DB_NAME || 'tiptop',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
  // Connection pool optimization
  max: parseInt(process.env.DB_POOL_SIZE || '20'),     // Maximum number of clients in the pool
  idleTimeoutMillis: 30000,                            // Close idle connections after 30s
  connectionTimeoutMillis: 2000,                       // Connection timeout
  maxUses: 7500                                        // Close connections after 7500 queries
});

// Mock in-memory database for testing with memory limits
const inMemoryMessages = new Map();

// Configuration for memory limits
const MEMORY_LIMITS = {
  MAX_MESSAGES_PER_URL: parseInt(process.env.MAX_MESSAGES_PER_URL || '100'),  // Maximum messages stored per URL
  MAX_MESSAGE_SIZE_BYTES: parseInt(process.env.MAX_MESSAGE_SIZE_BYTES || '10000'),  // Maximum size of a single message (10KB)
  MAX_URLS_STORED: parseInt(process.env.MAX_URLS_STORED || '1000'),  // Maximum number of URLs to store messages for
  MESSAGE_EXPIRY_MS: parseInt(process.env.MESSAGE_EXPIRY_MS || '86400000'),  // Messages expire after 24 hours
};

// Configuration for rate limiting
const RATE_LIMITS = {
  MESSAGES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_MESSAGES_PER_MINUTE || '30'),  // Maximum messages per minute per client
  PRESENCE_UPDATES_PER_MINUTE: parseInt(process.env.RATE_LIMIT_PRESENCE_PER_MINUTE || '10'),  // Maximum presence updates per minute
  RATE_LIMIT_WINDOW_MS: 60000,  // 1 minute window for rate limiting
};

// Rate limiting tracker
const clientRateLimits = new Map(); // userId -> { messageTimestamps: [], presenceTimestamps: [] }

// Initialize database
async function initDatabase() {
  try {
    console.log('Using in-memory database for testing');
    return true;
  } catch (err) {
    console.error('Error initializing database:', err);
    console.error('Database connection details:', {
      user: process.env.DB_USER,
      host: process.env.DB_HOST,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT
    });
    return false;
  }
}

// Store active connections by URL
const rooms = new Map();

// Store user information for reconnection tracking
const userInfo = new Map(); // userId -> { userName, lastSeen, rooms: Set of URLs }

// Function to create a consistent URL hash
function createUrlHash(url) {
  // Normalize the URL by removing trailing slashes, query parameters, etc.
  let normalizedUrl = url;
  try {
    const urlObj = new URL(url);
    // Use only the origin and pathname for hashing
    normalizedUrl = urlObj.origin + urlObj.pathname;
  } catch (e) {
    console.error('Error normalizing URL:', e);
    // Continue with the original URL if parsing fails
  }

  console.log(`Normalized URL for hashing: ${normalizedUrl}`);
  return Buffer.from(normalizedUrl).toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

// Function to save message to in-memory database with memory limits
async function saveMessage(message) {
  try {
    console.log('Saving message to in-memory database:', message);
    const pageUrlHash = createUrlHash(message.url);
    console.log(`URL hash for saving: ${pageUrlHash}`);

    // Check message size limit
    const messageSize = JSON.stringify(message).length;
    if (messageSize > MEMORY_LIMITS.MAX_MESSAGE_SIZE_BYTES) {
      console.warn(`Message size (${messageSize} bytes) exceeds limit (${MEMORY_LIMITS.MAX_MESSAGE_SIZE_BYTES} bytes), truncating content`);
      // Truncate message content to fit within size limit
      const contentSize = message.content.length;
      const maxContentSize = contentSize - (messageSize - MEMORY_LIMITS.MAX_MESSAGE_SIZE_BYTES) - 10; // Extra buffer
      message.content = message.content.substring(0, Math.max(10, maxContentSize)) + '... [truncated]';
    }

    const messageId = uuidv4();
    console.log(`Generated UUID for message: ${messageId}`);

    const timestamp = new Date(message.timestamp);
    console.log(`Parsed timestamp: ${timestamp.toISOString()}`);

    // Create a database record
    const record = {
      id: messageId,
      type: message.type,
      content: message.content,
      user_id: message.userId,
      user_name: message.userName,
      page_url: message.url,
      page_url_hash: pageUrlHash,
      message_id: message.messageId,
      timestamp: timestamp,
      expires_at: new Date(timestamp.getTime() + MEMORY_LIMITS.MESSAGE_EXPIRY_MS) // Add expiration time
    };

    // Enforce URL limit - if we're at the limit, remove the oldest URL's messages
    if (inMemoryMessages.size >= MEMORY_LIMITS.MAX_URLS_STORED && !inMemoryMessages.has(pageUrlHash)) {
      // Find the URL with the oldest message
      let oldestUrl = null;
      let oldestTimestamp = Date.now();

      inMemoryMessages.forEach((messages, url) => {
        if (messages.length > 0) {
          const urlOldestMessage = messages.reduce((oldest, msg) =>
            msg.timestamp < oldest.timestamp ? msg : oldest, messages[0]);

          if (urlOldestMessage.timestamp < oldestTimestamp) {
            oldestTimestamp = urlOldestMessage.timestamp;
            oldestUrl = url;
          }
        }
      });

      if (oldestUrl) {
        console.log(`URL limit reached, removing oldest URL: ${oldestUrl}`);
        inMemoryMessages.delete(oldestUrl);
      }
    }

    // Store in in-memory database
    if (!inMemoryMessages.has(pageUrlHash)) {
      inMemoryMessages.set(pageUrlHash, []);
    }

    // Get the messages for this URL
    const urlMessages = inMemoryMessages.get(pageUrlHash);

    // Add the new message
    urlMessages.push(record);

    // Enforce message limit per URL - remove oldest messages if over limit
    if (urlMessages.length > MEMORY_LIMITS.MAX_MESSAGES_PER_URL) {
      const excessCount = urlMessages.length - MEMORY_LIMITS.MAX_MESSAGES_PER_URL;
      console.log(`Message limit for URL exceeded, removing ${excessCount} oldest messages`);

      // Sort by timestamp (oldest first) and remove excess
      urlMessages.sort((a, b) => a.timestamp - b.timestamp);
      urlMessages.splice(0, excessCount);
    }

    // Remove expired messages
    const now = new Date();
    const expiredCount = urlMessages.filter(msg => msg.expires_at < now).length;
    if (expiredCount > 0) {
      console.log(`Removing ${expiredCount} expired messages`);
      const validMessages = urlMessages.filter(msg => msg.expires_at >= now);
      inMemoryMessages.set(pageUrlHash, validMessages);
    }

    console.log(`Message saved to in-memory database with ID: ${messageId}`);
    console.log(`Message content: "${message.content.substring(0, 30)}${message.content.length > 30 ? '...' : ''}"`);
    console.log(`Message URL: ${message.url}`);
    console.log(`Message URL hash: ${pageUrlHash}`);
    console.log(`Total messages for this URL: ${inMemoryMessages.get(pageUrlHash).length}`);

    return messageId;
  } catch (err) {
    console.error('Error saving message to in-memory database:', err);
    console.error(err.stack);
    return null;
  }
}

// Function to get recent messages for a URL from in-memory database
async function getRecentMessages(pageUrl, limit = 50) {
  try {
    console.log(`Getting recent messages for URL: ${pageUrl}, limit: ${limit}`);
    const pageUrlHash = createUrlHash(pageUrl);
    console.log(`URL hash: ${pageUrlHash}`);

    // Get messages from in-memory database
    const messagesForUrl = inMemoryMessages.get(pageUrlHash) || [];
    console.log(`In-memory database returned ${messagesForUrl.length} messages`);

    // Sort by timestamp (ascending - oldest first)
    const sortedMessages = [...messagesForUrl].sort((a, b) => {
      return new Date(a.timestamp) - new Date(b.timestamp);
    });

    // Apply limit
    const limitedMessages = sortedMessages.slice(0, limit);

    // Map to client format
    const messages = limitedMessages.map(row => ({
      type: row.type,
      content: row.content,
      userId: row.user_id,
      userName: row.user_name,
      url: row.page_url,
      messageId: row.message_id,
      timestamp: row.timestamp.toISOString()
    }));

    console.log(`Mapped ${messages.length} messages for client`);
    return messages;
  } catch (err) {
    console.error('Error getting recent messages:', err);
    console.error(err.stack);
    return [];
  }
}

// Function to get active users in a room
function getActiveUsers(roomUrl) {
  const room = rooms.get(roomUrl);
  if (!room) return [];

  // Collect unique user IDs and names from the room
  const users = new Map();

  // First, add all users with active connections in this room
  room.forEach(client => {
    if (client.userId && client.userName) {
      users.set(client.userId, {
        userId: client.userId,
        userName: client.userName
      });
    }
  });

  // Log the number of users found in the room
  console.log(`Found ${users.size} users with active connections in room ${roomUrl}`);

  // For debugging, log all users in the room
  if (users.size > 0) {
    console.log('Users in room:', Array.from(users.values()).map(u => `${u.userName} (${u.userId})`));
  }

  return Array.from(users.values());
}

// WebSocket connection handler
wss.on('connection', async (ws, req) => {
  const parameters = url.parse(req.url, true);
  let pageUrl = parameters.query.url;

  console.log('New WebSocket connection with URL parameter:', pageUrl);

  if (!pageUrl) {
    console.error('No URL parameter provided');
    ws.close(1003, 'URL parameter required');
    return;
  }

  // Try to decode the URL if it's encoded
  try {
    pageUrl = decodeURIComponent(pageUrl);
    console.log('Decoded URL:', pageUrl);
  } catch (e) {
    console.error('Error decoding URL:', e);
    // Continue with the original URL if decoding fails
  }

  // Add user to the room for this URL
  if (!rooms.has(pageUrl)) {
    rooms.set(pageUrl, new Set());
  }
  rooms.get(pageUrl).add(ws);

  console.log(`Client connected to room: ${pageUrl}`);

  // Set up heartbeat handling
  ws.isAlive = true;
  ws.on('pong', () => {
    ws.isAlive = true;
    console.log(`Received pong from client in room: ${pageUrl}`);
  });

  // Send recent messages from database
  try {
    console.log(`Retrieving history messages for URL: ${pageUrl}`);
    const recentMessages = await getRecentMessages(pageUrl);
    console.log(`Retrieved ${recentMessages.length} history messages to send to new client`);

    // Log the first few messages for debugging
    if (recentMessages.length > 0) {
      console.log('First message:', JSON.stringify(recentMessages[0]));
      if (recentMessages.length > 1) {
        console.log('Second message:', JSON.stringify(recentMessages[1]));
      }
    }

    if (recentMessages.length > 0) {
      // No system message for history loading - we'll just send the history directly
      console.log(`Preparing to send ${recentMessages.length} history messages`);

      // Short delay to ensure system message is processed first
      setTimeout(() => {
        const historyMessage = {
          type: 'history',
          messages: recentMessages,
          timestamp: new Date().toISOString()
        };
        console.log(`Sending history message with ${recentMessages.length} messages`);
        console.log('History message structure:', JSON.stringify({
          type: historyMessage.type,
          messageCount: historyMessage.messages.length,
          timestamp: historyMessage.timestamp
        }));

        try {
          const messageJson = JSON.stringify(historyMessage);
          console.log(`JSON string length: ${messageJson.length} characters`);
          ws.send(messageJson);
          console.log('History message sent successfully');
        } catch (jsonErr) {
          console.error('Error stringifying or sending history message:', jsonErr);
        }
      }, 500); // Increased delay to 500ms for more reliable delivery
    } else {
      console.log('No history messages to send');
    }
  } catch (err) {
    console.error('Error sending message history:', err);
    console.error(err.stack);
  }

  // Handle incoming messages
  ws.on('message', async (message) => {
    try {
      console.log(`Received message from client: ${message.toString().substring(0, 100)}...`);
      const parsedMessage = JSON.parse(message.toString());
      console.log(`Parsed message type: ${parsedMessage.type}`);

      // Add server timestamp
      parsedMessage.serverTimestamp = new Date().toISOString();

      // Handle presence updates
      if (parsedMessage.type === 'presence') {
        console.log(`Presence update from user: ${parsedMessage.userName} (${parsedMessage.userId})`);

        // Check rate limiting for presence updates
        if (parsedMessage.userId && isRateLimited(parsedMessage.userId, 'presence')) {
          console.warn(`Rate limiting presence update from ${parsedMessage.userName} (${parsedMessage.userId})`);
          try {
            ws.send(JSON.stringify({
              type: 'error',
              error: 'Rate limit exceeded for presence updates. Please try again later.',
              code: 'RATE_LIMIT_EXCEEDED',
              timestamp: new Date().toISOString()
            }));
          } catch (error) {
            console.error('Error sending rate limit message:', error);
          }
          return; // Skip processing this message
        }

        // Store user info with the connection
        ws.userId = parsedMessage.userId;
        ws.userName = parsedMessage.userName;

        // Update global user info for reconnection tracking
        if (!userInfo.has(parsedMessage.userId)) {
          userInfo.set(parsedMessage.userId, {
            userName: parsedMessage.userName,
            lastSeen: new Date(),
            rooms: new Set([pageUrl])
          });
          console.log(`Added new user to tracking: ${parsedMessage.userName} (${parsedMessage.userId})`);
        } else {
          // Update existing user info
          const info = userInfo.get(parsedMessage.userId);
          info.userName = parsedMessage.userName; // Update name in case it changed
          info.lastSeen = new Date();
          info.rooms.add(pageUrl);
          console.log(`Updated existing user: ${parsedMessage.userName} (${parsedMessage.userId})`);
          console.log(`User is now in ${info.rooms.size} rooms`);
        }

        // Broadcast updated user list to all clients in the room
        const activeUsers = getActiveUsers(pageUrl);
        console.log(`Active users in room ${pageUrl}: ${activeUsers.length}`);
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Broadcasting user list to ${room.size} clients`);

          // Prepare the user list message once
          const userListMessage = JSON.stringify({
            type: 'users',
            users: activeUsers,
            timestamp: new Date().toISOString()
          });

          // Use batching for large rooms
          const clients = Array.from(room);
          const totalClients = clients.length;
          const BATCH_SIZE = 25; // Process 25 clients at a time

          // Function to process a batch of clients
          const processBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + BATCH_SIZE, totalClients);

            for (let i = startIndex; i < endIndex; i++) {
              const client = clients[i];
              if (client.readyState === WebSocket.OPEN) {
                try {
                  client.send(userListMessage);
                } catch (sendErr) {
                  console.error('Error sending user list to client:', sendErr);
                }
              }
            }

            // If there are more clients to process, schedule the next batch
            if (endIndex < totalClients) {
              setImmediate(() => processBatch(endIndex));
            }
          };

          // Start processing the first batch
          processBatch(0);
        }
      }

      // Handle request_scroll messages
      else if (parsedMessage.type === 'request_scroll') {
        console.log(`Received request_scroll from client for URL: ${pageUrl}`);

        // Get the sender's ID for the message
        const senderId = ws.userId || 'unknown';

        // Send scroll command to all clients in the room with enhanced information
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Sending enhanced scroll command to ${room.size} clients in room`);

          // Send multiple scroll commands with increasing delays
          // This ensures clients receive scroll commands at different times,
          // increasing the chance of successful scrolling
          const scrollDelays = [100, 300, 600, 1000];

          scrollDelays.forEach((delay, index) => {
            setTimeout(() => {
              let sentCount = 0;

              room.forEach((client) => {
                if (client.readyState === WebSocket.OPEN) {
                  try {
                    client.send(JSON.stringify({
                      type: 'scroll_chat',
                      timestamp: new Date().toISOString(),
                      senderId: senderId,
                      forceScroll: true, // Force scroll for everyone to ensure visibility
                      messageId: parsedMessage.messageId || null,
                      attempt: index + 1,
                      totalAttempts: scrollDelays.length
                    }));
                    sentCount++;
                  } catch (sendErr) {
                    console.error('Error sending scroll command to client:', sendErr);
                  }
                }
              });

              console.log(`Enhanced scroll command (attempt ${index + 1}/${scrollDelays.length}) sent to ${sentCount} clients after ${delay}ms delay`);
            }, delay);
          });
        }
      }

      // Handle get_users requests
      else if (parsedMessage.type === 'get_users') {
        console.log(`Received get_users request from client for URL: ${pageUrl}`);
        console.log(`Request from user: ${ws.userName || 'Unknown'} (${ws.userId || 'Unknown ID'})`);

        // Get active users for this room
        const activeUsers = getActiveUsers(pageUrl);
        console.log(`Active users in room ${pageUrl}: ${activeUsers.length}`);

        // Log room size and connection details
        const room = rooms.get(pageUrl);
        if (room) {
          console.log(`Room size: ${room.size} connections`);
          let openCount = 0;
          let closingCount = 0;
          let closedCount = 0;
          let connectingCount = 0;

          room.forEach(client => {
            if (client.readyState === WebSocket.OPEN) openCount++;
            else if (client.readyState === WebSocket.CLOSING) closingCount++;
            else if (client.readyState === WebSocket.CLOSED) closedCount++;
            else if (client.readyState === WebSocket.CONNECTING) connectingCount++;
          });

          console.log(`Connection states in room: Open=${openCount}, Connecting=${connectingCount}, Closing=${closingCount}, Closed=${closedCount}`);
        } else {
          console.log('Room not found for this URL');
        }

        // Send the user list back to the requesting client only
        try {
          const response = {
            type: 'users',
            users: activeUsers,
            timestamp: new Date().toISOString()
          };

          const responseJson = JSON.stringify(response);
          ws.send(responseJson);

          console.log(`Sent user list with ${activeUsers.length} users to requesting client`);
          if (activeUsers.length > 0) {
            console.log('User list sent:', activeUsers.map(u => `${u.userName} (${u.userId})`));
          }
        } catch (error) {
          console.error('Error sending user list to client:', error);
        }
      }

      // Handle get_history requests
      else if (parsedMessage.type === 'get_history') {
        console.log(`Received get_history request from client for URL: ${pageUrl}`);
        console.log(`Request from user: ${ws.userName || 'Unknown'} (${ws.userId || 'Unknown ID'})`);

        // Get recent messages for this URL
        try {
          const recentMessages = await getRecentMessages(pageUrl);
          console.log(`Retrieved ${recentMessages.length} history messages to send to client`);

          if (recentMessages.length > 0) {
            // Send history messages to the client
            const historyMessage = {
              type: 'history',
              messages: recentMessages,
              timestamp: new Date().toISOString()
            };

            const messageJson = JSON.stringify(historyMessage);
            ws.send(messageJson);

            console.log(`Sent history with ${recentMessages.length} messages to requesting client`);
          } else {
            console.log('No history messages found for this URL');
          }
        } catch (error) {
          console.error('Error retrieving history messages:', error);
        }
      }

      // Save message to database (except presence updates)
      if (parsedMessage.type === 'chat' || parsedMessage.type === 'note') {
        // Check rate limiting for chat/note messages
        if (parsedMessage.userId && isRateLimited(parsedMessage.userId, parsedMessage.type)) {
          console.warn(`Rate limiting ${parsedMessage.type} message from ${parsedMessage.userName} (${parsedMessage.userId})`);
          try {
            ws.send(JSON.stringify({
              type: 'error',
              error: `Rate limit exceeded for ${parsedMessage.type} messages. Please try again later.`,
              code: 'RATE_LIMIT_EXCEEDED',
              timestamp: new Date().toISOString()
            }));
          } catch (error) {
            console.error('Error sending rate limit message:', error);
          }
          return; // Skip processing this message
        }

        console.log(`Saving ${parsedMessage.type} message to database`);
        const savedId = await saveMessage(parsedMessage);
        console.log(`Message saved with ID: ${savedId}`);
      }

      // Broadcast to all clients in the same room with batching for large rooms
      const room = rooms.get(pageUrl);
      if (room) {
        console.log(`Broadcasting message to ${room.size} clients in room`);
        let sentCount = 0;

        // Prepare the message JSON string once
        const messageJson = JSON.stringify(parsedMessage);

        // Use batching for large rooms to prevent event loop blocking
        const BATCH_SIZE = 25; // Process 25 clients at a time
        const clients = Array.from(room);
        const totalClients = clients.length;

        // Function to process a batch of clients
        const processBatch = (startIndex) => {
          const endIndex = Math.min(startIndex + BATCH_SIZE, totalClients);

          for (let i = startIndex; i < endIndex; i++) {
            const client = clients[i];
            if (client.readyState === WebSocket.OPEN) {
              try {
                client.send(messageJson);
                sentCount++;
              } catch (sendErr) {
                console.error('Error sending message to client:', sendErr);
              }
            }
          }

          // If there are more clients to process, schedule the next batch
          if (endIndex < totalClients) {
            setImmediate(() => processBatch(endIndex));
          } else {
            console.log(`Message broadcast to ${sentCount}/${totalClients} clients`);
          }
        };

        // Start processing the first batch
        processBatch(0);

        // For chat messages, also send an enhanced scroll command to all clients
        if (parsedMessage.type === 'chat') {
          console.log('Sending enhanced scroll command to all clients in the room');

          // Get the sender's ID for the message
          const senderId = ws.userId || 'unknown';

          // Longer delay to ensure the message is processed and rendered first
          setTimeout(() => {
            let scrollSentCount = 0;

            // Create the scroll command once
            const scrollCommand = {
              type: 'scroll_chat',
              timestamp: new Date().toISOString(),
              senderId: senderId,
              messageId: parsedMessage.messageId || null
            };

            // Use batching for large rooms
            const clients = Array.from(room);
            const totalClients = clients.length;

            // Function to process a batch of scroll commands
            const processScrollBatch = (startIndex) => {
              const endIndex = Math.min(startIndex + BATCH_SIZE, totalClients);

              for (let i = startIndex; i < endIndex; i++) {
                const client = clients[i];
                if (client.readyState === WebSocket.OPEN) {
                  try {
                    // Customize the force scroll parameter for each client
                    scrollCommand.forceScroll = client !== ws; // Force scroll for everyone except the sender
                    client.send(JSON.stringify(scrollCommand));
                    scrollSentCount++;
                  } catch (sendErr) {
                    console.error('Error sending scroll command to client:', sendErr);
                  }
                }
              }

              // If there are more clients to process, schedule the next batch
              if (endIndex < totalClients) {
                setImmediate(() => processScrollBatch(endIndex));
              } else {
                console.log(`Enhanced scroll command sent to ${scrollSentCount}/${totalClients} clients`);
              }
            };

            // Start processing the first batch
            processScrollBatch(0);
          }, 200); // Increased delay for better reliability
        }
      } else {
        console.log(`No room found for URL: ${pageUrl}`);
      }
    } catch (e) {
      console.error('Error processing message:', e);
      console.error(e.stack);
    }
  });

  // Handle disconnection
  ws.on('close', () => {
    console.log(`Client disconnected from room: ${pageUrl}`);
    if (ws.userId) {
      console.log(`Disconnected user: ${ws.userName || 'Unknown'} (${ws.userId})`);
    }

    // Remove from room
    const room = rooms.get(pageUrl);
    if (room) {
      // Check if this user has other connections in the same room
      let hasOtherConnections = false;
      const userId = ws.userId;

      if (userId) {
        room.forEach(client => {
          if (client !== ws && client.userId === userId && client.readyState === WebSocket.OPEN) {
            hasOtherConnections = true;
            console.log(`User ${userId} has other active connections in the same room`);
          }
        });
      }

      // Remove this specific connection
      room.delete(ws);
      console.log(`Removed connection from room. New room size: ${room.size}`);

      // Broadcast updated user list only if this was the user's last connection
      if (ws.userId && !hasOtherConnections) {
        console.log(`Broadcasting user list update after ${ws.userName || 'Unknown'} (${ws.userId}) disconnected`);
        const activeUsers = getActiveUsers(pageUrl);

        // Update user info tracking
        if (userInfo.has(ws.userId)) {
          const info = userInfo.get(ws.userId);
          info.rooms.delete(pageUrl);
          console.log(`Updated user tracking: ${ws.userId} removed from room ${pageUrl}`);
          console.log(`User is now in ${info.rooms.size} rooms`);
        }

        // Send updated user list to all clients in the room
        room.forEach((client) => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
              type: 'users',
              users: activeUsers,
              timestamp: new Date().toISOString()
            }));
          }
        });
      }

      // Clean up empty rooms
      if (room.size === 0) {
        console.log(`Room ${pageUrl} is now empty, removing it`);
        rooms.delete(pageUrl);
      }
    } else {
      console.log(`Room not found for URL: ${pageUrl}`);
    }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).send('OK');
});

// Status endpoint with enhanced monitoring information
app.get('/status', (req, res) => {
  // Count connections by state
  let openConnections = 0;
  let closingConnections = 0;
  let closedConnections = 0;
  let connectingConnections = 0;
  let totalConnections = 0;

  // Count users and messages
  const activeUsers = new Set();
  let totalMessages = 0;

  // Gather room statistics
  const roomStats = [];

  rooms.forEach((room, url) => {
    let roomOpenConnections = 0;
    let roomClosingConnections = 0;
    let roomClosedConnections = 0;
    let roomConnectingConnections = 0;

    room.forEach(client => {
      totalConnections++;

      if (client.readyState === WebSocket.OPEN) {
        openConnections++;
        roomOpenConnections++;
      } else if (client.readyState === WebSocket.CLOSING) {
        closingConnections++;
        roomClosingConnections++;
      } else if (client.readyState === WebSocket.CLOSED) {
        closedConnections++;
        roomClosedConnections++;
      } else if (client.readyState === WebSocket.CONNECTING) {
        connectingConnections++;
        roomConnectingConnections++;
      }

      if (client.userId) {
        activeUsers.add(client.userId);
      }
    });

    // Get message count for this URL
    const urlHash = createUrlHash(url);
    const messagesForUrl = inMemoryMessages.get(urlHash) || [];
    totalMessages += messagesForUrl.length;

    // Add room statistics
    roomStats.push({
      url: url.substring(0, 100) + (url.length > 100 ? '...' : ''), // Truncate long URLs
      connections: room.size,
      openConnections: roomOpenConnections,
      messages: messagesForUrl.length
    });
  });

  // Sort room stats by connection count (descending)
  roomStats.sort((a, b) => b.connections - a.connections);

  // Limit to top 10 rooms
  const topRooms = roomStats.slice(0, 10);

  // Get rate limit statistics
  const rateLimitedUsers = clientRateLimits.size;

  // Create the status object
  const status = {
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    memory: {
      heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100 + ' MB',
      heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100 + ' MB',
      rss: Math.round(process.memoryUsage().rss / 1024 / 1024 * 100) / 100 + ' MB'
    },
    connections: {
      total: totalConnections,
      open: openConnections,
      closing: closingConnections,
      closed: closedConnections,
      connecting: connectingConnections
    },
    rooms: {
      count: rooms.size,
      topRooms: topRooms
    },
    users: {
      count: activeUsers.size,
      rateLimited: rateLimitedUsers
    },
    messages: {
      total: totalMessages,
      urlsTracked: inMemoryMessages.size
    },
    config: {
      maxMessagesPerUrl: MEMORY_LIMITS.MAX_MESSAGES_PER_URL,
      messageExpiryHours: Math.round(MEMORY_LIMITS.MESSAGE_EXPIRY_MS / 1000 / 60 / 60),
      rateLimit: {
        messagesPerMinute: RATE_LIMITS.MESSAGES_PER_MINUTE,
        presenceUpdatesPerMinute: RATE_LIMITS.PRESENCE_UPDATES_PER_MINUTE
      }
    }
  };

  res.status(200).json(status);
});

// Function to insert a test message (for debugging)
async function insertTestMessage() {
  try {
    const testUrl = 'https://example.com';
    const testUrlHash = createUrlHash(testUrl);

    console.log('Inserting test message with URL hash:', testUrlHash);

    // Create test message
    const message = {
      type: 'chat',
      content: 'This is a test message',
      userId: 'test-user-id',
      userName: 'Test User',
      url: testUrl,
      messageId: `test-msg-${Date.now()}`,
      timestamp: new Date().toISOString()
    };

    // Save to in-memory database
    await saveMessage(message);

    console.log('Test message inserted successfully');

    // Insert a second test message
    const message2 = {
      type: 'chat',
      content: 'This is another test message',
      userId: 'test-user-id-2',
      userName: 'Another Test User',
      url: testUrl,
      messageId: `test-msg-${Date.now() + 1}`,
      timestamp: new Date().toISOString()
    };

    await saveMessage(message2);

    // Verify the messages were inserted
    const messages = await getRecentMessages(testUrl, 10);
    console.log(`Retrieved ${messages.length} messages for test URL`);
  } catch (err) {
    console.error('Error inserting test message:', err);
  }
}

// Function to check if a client is rate limited
function isRateLimited(userId, messageType) {
  if (!userId) return false; // Skip rate limiting for users without IDs

  // Initialize rate limit tracking for this user if it doesn't exist
  if (!clientRateLimits.has(userId)) {
    clientRateLimits.set(userId, {
      messageTimestamps: [],
      presenceTimestamps: []
    });
  }

  const userRateLimits = clientRateLimits.get(userId);
  const now = Date.now();
  const windowStart = now - RATE_LIMITS.RATE_LIMIT_WINDOW_MS;

  // Clean up old timestamps outside the window
  if (messageType === 'chat' || messageType === 'note') {
    userRateLimits.messageTimestamps = userRateLimits.messageTimestamps.filter(
      timestamp => timestamp > windowStart
    );
  } else if (messageType === 'presence') {
    userRateLimits.presenceTimestamps = userRateLimits.presenceTimestamps.filter(
      timestamp => timestamp > windowStart
    );
  }

  // Check if user is over the limit
  if (messageType === 'chat' || messageType === 'note') {
    if (userRateLimits.messageTimestamps.length >= RATE_LIMITS.MESSAGES_PER_MINUTE) {
      console.warn(`User ${userId} is rate limited for messages: ${userRateLimits.messageTimestamps.length} messages in the last minute`);
      return true;
    }
    // Add current timestamp for this message
    userRateLimits.messageTimestamps.push(now);
  } else if (messageType === 'presence') {
    if (userRateLimits.presenceTimestamps.length >= RATE_LIMITS.PRESENCE_UPDATES_PER_MINUTE) {
      console.warn(`User ${userId} is rate limited for presence updates: ${userRateLimits.presenceTimestamps.length} updates in the last minute`);
      return true;
    }
    // Add current timestamp for this presence update
    userRateLimits.presenceTimestamps.push(now);
  }

  return false;
}

// Function to clean up stale connections and rate limit data
function cleanupStaleConnections() {
  console.log('Running periodic cleanup of stale connections and rate limit data');

  let totalConnections = 0;
  let closedConnections = 0;

  // Check each room
  rooms.forEach((room, url) => {
    console.log(`Checking room ${url} with ${room.size} connections`);
    totalConnections += room.size;

    // Find closed connections
    const staleConnections = [];
    room.forEach(client => {
      if (client.readyState === WebSocket.CLOSED || client.readyState === WebSocket.CLOSING) {
        staleConnections.push(client);
      }
    });

    // Remove stale connections
    if (staleConnections.length > 0) {
      console.log(`Removing ${staleConnections.length} stale connections from room ${url}`);
      staleConnections.forEach(client => {
        room.delete(client);
        closedConnections++;
      });

      // If room is now empty, remove it
      if (room.size === 0) {
        console.log(`Room ${url} is now empty after cleanup, removing it`);
        rooms.delete(url);
      } else {
        // Broadcast updated user list
        const activeUsers = getActiveUsers(url);
        room.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify({
              type: 'users',
              users: activeUsers,
              timestamp: new Date().toISOString()
            }));
          }
        });
      }
    }
  });

  // Clean up rate limit data for users who are no longer connected
  const now = Date.now();
  const activeUserIds = new Set();

  // Collect all active user IDs
  rooms.forEach(room => {
    room.forEach(client => {
      if (client.userId) {
        activeUserIds.add(client.userId);
      }
    });
  });

  // Remove rate limit data for inactive users
  let removedRateLimitEntries = 0;
  clientRateLimits.forEach((data, userId) => {
    if (!activeUserIds.has(userId)) {
      clientRateLimits.delete(userId);
      removedRateLimitEntries++;
    }
  });

  if (removedRateLimitEntries > 0) {
    console.log(`Removed rate limit data for ${removedRateLimitEntries} inactive users`);
  }

  console.log(`Cleanup complete: Checked ${totalConnections} connections, removed ${closedConnections} stale connections`);
  console.log(`Remaining rooms: ${rooms.size}, Rate limited users: ${clientRateLimits.size}`);
}

// Start the server
const PORT = process.env.PORT || 8080;
server.listen(PORT, async () => {
  console.log(`WebSocket server listening on port ${PORT}`);
  await initDatabase();

  // Insert a test message if DEBUG is enabled
  if (process.env.DEBUG === 'true') {
    console.log('DEBUG mode enabled, inserting test message');
    await insertTestMessage();
  }

  // Set up periodic cleanup of stale connections
  setInterval(cleanupStaleConnections, 60000); // Run every minute
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  // Clear the heartbeat interval
  clearInterval(heartbeatInterval);
  console.log('Heartbeat interval cleared');

  // Close all WebSocket connections
  wss.clients.forEach(client => {
    client.terminate();
  });
  console.log('All WebSocket connections terminated');

  server.close(() => {
    console.log('HTTP server closed');
    pool.end(() => {
      console.log('Database connection pool closed');
      process.exit(0);
    });
  });
});
