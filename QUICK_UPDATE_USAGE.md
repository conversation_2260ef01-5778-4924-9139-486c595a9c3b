# TipTop Quick Update Script Usage

## Overview
The `quick-update.sh` script has been enhanced to support selective updates of TipTop services with three options:

1. **Update only tiptop-server** (Cloud Function/API)
2. **Update only websocket-server** (Chat/Social features)
3. **Update both services** (Default)

## Usage

### Basic Syntax
```bash
./quick-update.sh <GCP_PROJECT_ID> [UPDATE_TYPE]
```

### Update Options

#### 1. Update Only TipTop Server (Cloud Function)
```bash
./quick-update.sh your-gcp-project-123 server
```
**What this updates:**
- ✅ TipTop Cloud Function (API server)
- ❌ WebSocket server (unchanged)

**Use when:**
- You've made changes to the main API/cloud function
- You want to update AI features, content processing, etc.
- WebSocket/chat features are working fine

#### 2. Update Only WebSocket Server
```bash
./quick-update.sh your-gcp-project-123 websocket
```
**What this updates:**
- ❌ TipTop Cloud Function (unchanged)
- ✅ WebSocket server (chat/social features)

**Use when:**
- You've made changes to chat message ordering (like our recent fixes)
- You want to update social features, user presence, etc.
- Main API is working fine

#### 3. Update Both Services (Default)
```bash
./quick-update.sh your-gcp-project-123 both
# OR simply:
./quick-update.sh your-gcp-project-123
```
**What this updates:**
- ✅ TipTop Cloud Function (API server)
- ✅ WebSocket server (chat/social features)

**Use when:**
- You've made changes to both services
- You want to ensure everything is up to date
- First time deployment or major updates

## For Your Current Chat Fixes

Since we made changes to the WebSocket server for chat message ordering, you should run:

```bash
cd tiptop-server
./quick-update.sh YOUR_GCP_PROJECT_ID websocket
```

This will:
1. ✅ Build new WebSocket server image with chat ordering fixes
2. ✅ Push to Google Container Registry
3. ✅ Perform zero-downtime rolling update
4. ✅ Monitor deployment status
5. ✅ Verify the update completed successfully

## What the Script Does

### For WebSocket Updates:
1. **Builds** new Docker image with timestamp tag
2. **Pushes** to `gcr.io/YOUR_PROJECT/tiptop-websocket:TIMESTAMP`
3. **Updates** Kubernetes deployment with new image
4. **Monitors** rollout status (zero downtime)
5. **Verifies** pods are running correctly

### Key Features:
- ✅ **Zero downtime** - Rolling updates keep service running
- ✅ **Timestamp tags** - Each build gets unique tag for rollback
- ✅ **Validation** - Checks deployments exist before updating
- ✅ **Monitoring** - Watches rollout progress
- ✅ **Selective updates** - Only update what you need

## Expected Output

```bash
🚀 TipTop Quick Rolling Update
📋 Project ID: your-project-123
🎯 Update Type: websocket
🔄 This will perform a zero-downtime update

🔍 Checking required tools...
🔧 Setting GCP project...
🐳 Configuring Docker for Google Container Registry...
📊 Checking current deployment status...
✅ Found WebSocket deployment

🏗️  Building WebSocket server image with tag: 20240617-143022...
📤 Pushing WebSocket server image...
✅ WebSocket server image built and pushed

🔄 Starting rolling updates...
🔄 Performing rolling update for WebSocket server...
📊 Monitoring WebSocket server rollout...
deployment "tiptop-websocket" successfully rolled out
✅ WebSocket server update completed

✅ Rolling update completed successfully!
🎯 Updated: websocket

📊 Current Status:
NAME                                                READY   STATUS    RESTARTS   AGE
tiptop-websocket-NEW_HASH-xxxxx                     1/1     Running   0          2m
tiptop-websocket-NEW_HASH-yyyyy                     1/1     Running   0          1m
tiptop-websocket-NEW_HASH-zzzzz                     1/1     Running   0          30s

🔍 Verify the update:
  - WebSocket logs: kubectl logs deployment/tiptop-websocket -n tiptop
  - Test WebSocket: Open browser console and test connection

🎯 The update was performed with zero downtime!
📦 Updated: WebSocket server only
💡 This includes the chat message ordering improvements
```

## Troubleshooting

### If Update Fails:
1. **Check logs**: `kubectl logs deployment/tiptop-websocket -n tiptop`
2. **Check pods**: `kubectl get pods -n tiptop`
3. **Rollback if needed**: `kubectl rollout undo deployment/tiptop-websocket -n tiptop`

### If Connection Issues:
1. **Verify service**: `kubectl get services -n tiptop`
2. **Check ingress**: `kubectl get ingress -n tiptop`
3. **Test locally**: Use browser console to test WebSocket connection

## Next Steps After Update

1. **Test the chat features** in your browser
2. **Check message ordering** is working correctly
3. **Verify no messages are lost** on reconnection
4. **Monitor logs** for any errors

The chat message ordering improvements should now be live! 🎉
