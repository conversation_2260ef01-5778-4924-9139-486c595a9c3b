// TipTop Social Client (social-client.js)

// Load configuration
let TipTopConfig = {
  isTestMode: false,
  wsUrls: [
    'wss://ws.tiptop.qubitrhythm.com',
    'ws://localhost:8080'
  ],
  features: {
    useMockWebSocket: false,
  }
};

// Try to load configuration from localStorage
try {
  const isTestMode = localStorage.getItem('TIPTOP_TEST_MODE') === 'true';

  TipTopConfig = {
    isTestMode: isTestMode,
    wsUrls: isTestMode
      ? ['ws://localhost:8080', 'ws://localhost:30081/ws', 'wss://ws.tiptop.qubitrhythm.com']
      : ['wss://ws.tiptop.qubitrhythm.com', 'wss://tiptop.qubitrhythm.com/ws'],
    features: {
      useMockWebSocket: localStorage.getItem('TIPTOP_USE_MOCK_WEBSOCKET') === 'true',
    }
  };
} catch (e) {
  console.error('Error loading configuration from localStorage:', e);
}

// WebSocket connection
let socket = null;
let userId = null;
let userName = null;
let isConnected = false;
let reconnectAttempts = 0;
let socialEnabled = false; // Default to disabled (opt-in approach)
let activeUsers = []; // Array to store active users
let useMockWebSocket = TipTopConfig.features.useMockWebSocket; // Use configuration
const MAX_RECONNECT_ATTEMPTS = 5;

// Track retry timeouts so they can be cleared if needed
window.tiptopRetryTimeouts = [];

// Initialize the social client
async function initializeSocialClient() {
  console.log('Initializing TipTop social client');

  try {
    // Load user preferences
    await loadUserPreferences();

    // Generate or load user ID
    await initializeUserId();

    console.log('Social client initialized with userId:', userId);
    console.log('Social client initialized with userName:', userName);
    console.log('Social features enabled:', socialEnabled);

    // Check if social features are enabled
    if (!socialEnabled) {
      console.log('TipTop social features are disabled by user preference');
      return;
    }

    // Verify we have a valid user ID before connecting
    if (!userId) {
      console.error('Failed to initialize user ID');
      showNotification('Failed to initialize chat. Please refresh the page.', 'error');
      return;
    }

    // Connect to WebSocket server
    connectWebSocket();

    // Set up storage change listener for cross-browser communication
    setupStorageChangeListener();

    // Set up runtime message listener for direct communication
    setupRuntimeMessageListener();

    // SIMPLIFIED: No local message loading - server is single source of truth
    console.log('Social client initialized - will load messages from server when connected');
  } catch (error) {
    console.error('Error initializing social client:', error);
    showNotification('Failed to initialize chat: ' + error.message, 'error');
  }
}

// Load existing shared messages for this page
function loadExistingSharedMessages() {
  const currentUrl = window.location.href;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const messagesKey = `tiptop_msgs_${urlKey}`;

  console.log('Loading existing shared messages for URL:', currentUrl);
  console.log('Using storage key:', messagesKey);

  // First try to load from sync storage
  chrome.storage.sync.get([messagesKey], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing sync storage:', chrome.runtime.lastError);
      // Fall back to local storage
      loadFromLocalStorage();
      return;
    }

    const urlMessages = result[messagesKey] || [];
    console.log(`Found ${urlMessages.length} existing shared messages in sync storage`);

    // Process messages from sync storage
    processMessages(urlMessages);

    // Also check local storage for any additional messages
    loadFromLocalStorage();
  });

  // Function to load messages from local storage
  function loadFromLocalStorage() {
    chrome.storage.local.get([messagesKey], function(result) {
      if (chrome.runtime.lastError) {
        console.error('Error accessing local storage:', chrome.runtime.lastError);
        return;
      }

      const urlMessages = result[messagesKey] || [];
      console.log(`Found ${urlMessages.length} existing shared messages in local storage`);

      // Process messages from local storage
      processMessages(urlMessages);

      // Force scroll to bottom after loading messages
      setTimeout(() => {
        scrollChatToBottom(true);

        // Also use the dedicated function for more aggressive scrolling
        setTimeout(() => {
          forceScrollChatOnReopen();
        }, 200);
      }, 100);

      // Clean up old timestamp entries
      cleanupOldTimestampEntries();
    });
  }

  // Enhanced message processing with proper ordering and no limits
  function processMessages(urlMessages) {
    if (urlMessages.length > 0) {
      console.log(`Processing ${urlMessages.length} messages from storage`);

      // Add all messages to the message queue for unified processing
      urlMessages.forEach(msg => {
        if (!processedMessageIds.has(msg.messageId)) {
          // Mark the message as coming from history/storage
          msg.isFromHistory = true;
          msg.source = 'storage';

          // Add to message queue
          messageQueue.set(msg.messageId, msg);
        }
      });

      // Process the message queue with proper ordering
      processMessageQueue();
    }
  }
}

// Clean up old timestamp entries
function cleanupOldTimestampEntries() {
  // Clean up sync storage
  cleanupStorage('sync');

  // Clean up local storage
  cleanupStorage('local');

  function cleanupStorage(storageType) {
    const storage = storageType === 'sync' ? chrome.storage.sync : chrome.storage.local;

    storage.get(null, function(items) {
      if (chrome.runtime.lastError) {
        console.error(`Error accessing ${storageType} storage:`, chrome.runtime.lastError);
        return;
      }

      const keysToRemove = [];
      const now = Date.now();
      const ONE_HOUR = 60 * 60 * 1000;

      // Find old timestamp entries
      Object.keys(items).forEach(key => {
        // Clean up old timestamp format
        if (key.startsWith('tiptop_msg_timestamp_')) {
          keysToRemove.push(key);
        }

        // Clean up new timestamp format
        if (key.startsWith('tiptop_ts_')) {
          const timestampStr = key.replace('tiptop_ts_', '');
          const timestamp = parseInt(timestampStr, 10);

          // If older than 1 hour, mark for removal
          if (now - timestamp > ONE_HOUR) {
            keysToRemove.push(key);
          }
        }
      });

      // Remove old entries if any found
      if (keysToRemove.length > 0) {
        console.log(`Removing ${keysToRemove.length} old timestamp entries from ${storageType} storage`);
        storage.remove(keysToRemove);
      }
    });
  }
}

// Set up runtime message listener for direct communication between extension instances
function setupRuntimeMessageListener() {
  chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
    // Only process messages from other instances of our extension
    if (sender.id !== chrome.runtime.id) {
      return;
    }

    // Handle chat messages
    if (message.type === 'TIPTOP_CHAT_MESSAGE') {
      const data = message.data;

      // Only process messages for the current URL
      if (data.url !== window.location.href) {
        return;
      }

      // Skip our own messages
      if (data.userId === userId) {
        return;
      }

      // Skip messages we've already processed
      if (processedMessageIds.has(data.messageId)) {
        return;
      }

      console.log('Received chat message via runtime messaging:', data);

      // Add the message to the UI
      addChatMessage(
        data.userId,
        data.userName,
        data.content,
        data.timestamp,
        false,
        data.messageId
      );

      // Send a response to acknowledge receipt
      sendResponse({ received: true });
    }
  });

  console.log('Runtime message listener set up');
}

// Load user preferences from storage
async function loadUserPreferences() {
  return new Promise((resolve) => {
    // Try to load from sync storage first
    chrome.storage.sync.get(['tiptopSocialEnabled', 'tiptopUserName'], function(result) {
      if (chrome.runtime.lastError) {
        console.error('Error accessing sync storage for preferences:', chrome.runtime.lastError);
        // Fall back to local storage
        loadFromLocalStorage();
        return;
      }

      // If we got preferences from sync storage, use them
      if (result.tiptopSocialEnabled !== undefined || result.tiptopUserName) {
        applyPreferences(result);
        resolve();
      } else {
        // If not found in sync storage, try local storage
        loadFromLocalStorage();
      }
    });

    // Function to load from local storage as fallback
    function loadFromLocalStorage() {
      chrome.storage.local.get(['tiptopSocialEnabled', 'tiptopUserName'], function(result) {
        if (chrome.runtime.lastError) {
          console.error('Error accessing local storage for preferences:', chrome.runtime.lastError);
          // Use defaults if both storages fail
          applyPreferences({});
          resolve();
          return;
        }

        applyPreferences(result);

        // Save to sync storage for future use
        chrome.storage.sync.set({
          tiptopSocialEnabled: socialEnabled,
          tiptopUserName: userName
        });

        resolve();
      });
    }

    // Function to apply preferences from storage result
    function applyPreferences(result) {
      // If undefined (first time), default to false (opt-in)
      socialEnabled = result.tiptopSocialEnabled === true;

      // If user has set a name, use it
      if (result.tiptopUserName) {
        userName = result.tiptopUserName;
      }
      // Otherwise use a random name
      else {
        userName = window.TipTopRandomNames ? window.TipTopRandomNames.getRandomName() : 'Anonymous User';
      }

      console.log('TipTop social features enabled:', socialEnabled);
      console.log('TipTop user name:', userName);
    }
  });
}

// Initialize user ID from storage or create a new one
async function initializeUserId() {
  return new Promise((resolve, reject) => {
    try {
      // Try to load from sync storage first
      chrome.storage.sync.get(['tiptopUserId'], function(result) {
        if (chrome.runtime.lastError) {
          console.error('Error accessing sync storage for user ID:', chrome.runtime.lastError);
          // Fall back to local storage
          loadFromLocalStorage();
          return;
        }

        if (result && result.tiptopUserId) {
          userId = result.tiptopUserId;
          console.log('Loaded existing TipTop user ID from sync storage:', userId);
          resolve(userId);
        } else {
          // If not found in sync storage, try local storage
          loadFromLocalStorage();
        }
      });

      // Function to load from local storage as fallback
      function loadFromLocalStorage() {
        chrome.storage.local.get(['tiptopUserId'], function(result) {
          if (chrome.runtime.lastError) {
            console.error('Error accessing local storage for user ID:', chrome.runtime.lastError);
            // Generate new ID if both storages fail
            generateNewUserId();
            return;
          }

          if (result && result.tiptopUserId) {
            userId = result.tiptopUserId;
            console.log('Loaded existing TipTop user ID from local storage:', userId);

            // Save to sync storage for future use
            chrome.storage.sync.set({ tiptopUserId: userId });

            resolve(userId);
          } else {
            // Generate new ID if not found in either storage
            generateNewUserId();
          }
        });
      }

      // Function to generate a new user ID
      function generateNewUserId() {
        userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);

        // Save to both storages
        chrome.storage.sync.set({ tiptopUserId: userId }, function() {
          if (chrome.runtime.lastError) {
            console.error('Error saving user ID to sync storage:', chrome.runtime.lastError);
            // Fall back to local storage only
            saveToLocalOnly();
            return;
          }

          // Also save to local as backup
          chrome.storage.local.set({ tiptopUserId: userId });

          console.log('Generated new TipTop user ID:', userId);
          resolve(userId);
        });

        // Function to save to local storage only if sync fails
        function saveToLocalOnly() {
          chrome.storage.local.set({ tiptopUserId: userId }, function() {
            if (chrome.runtime.lastError) {
              console.error('Error saving user ID to local storage:', chrome.runtime.lastError);
              reject(new Error('Failed to save user ID to any storage'));
              return;
            }

            console.log('Generated new TipTop user ID (local only):', userId);
            resolve(userId);
          });
        }
      }
    } catch (error) {
      console.error('Unexpected error in initializeUserId:', error);
      reject(error);
    }
  });
}

// Connect to WebSocket server
function connectWebSocket() {
  console.log('connectWebSocket called');

  // Check if we already have an active connection
  if (socket) {
    console.log('Current WebSocket state:', socket.readyState);
    if (socket.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return; // Already connected
    } else if (socket.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket already connecting');
      return; // Already connecting
    } else {
      // Socket exists but is closing or closed, clean it up
      console.log('Cleaning up existing socket in state:', socket.readyState);
      try {
        socket.close();
      } catch (e) {
        console.error('Error closing existing socket:', e);
      }
      socket = null;
    }
  }

  // Make sure we have a user ID and name before connecting
  if (!userId) {
    console.error('Cannot connect: userId is not set');
    // Don't show error notification to user, just log it
    return;
  }

  if (!userName) {
    console.log('userName not set, using default');
    userName = 'Anonymous User';
  }

  // Encode the current URL to pass as a parameter
  const encodedUrl = encodeURIComponent(window.location.href);
  console.log('Connecting with URL:', encodedUrl);
  console.log('Connecting with userId:', userId);
  console.log('Connecting with userName:', userName);

  // Use WebSocket URLs from configuration
  // This helps with development and fallback scenarios
  const wsUrls = TipTopConfig.wsUrls.map(baseUrl => {
    // Add URL parameters based on the endpoint format
    if (baseUrl.includes('/ws')) {
      // Legacy format with userId and userName in URL
      return `${baseUrl}?url=${encodedUrl}&userId=${userId}&userName=${encodeURIComponent(userName)}`;
    } else {
      // New format with just URL parameter
      return `${baseUrl}?url=${encodedUrl}`;
    }
  });

  // For demo/testing, set mock mode to false to try real connections first
  useMockWebSocket = false;

  // If we're using mock mode, skip real connection attempts
  if (useMockWebSocket) {
    console.log('Using mock WebSocket mode by default, skipping real connection attempts');
    createMockWebSocket();
    return;
  }

  // Try to connect using the first URL
  tryConnect(0);

  function tryConnect(index) {
    if (index >= wsUrls.length) {
      console.log('All WebSocket connection attempts failed, using mock WebSocket');
      createMockWebSocket();
      return;
    }

    const wsUrl = wsUrls[index];
    console.log(`Connecting to WebSocket server (attempt ${index + 1}/${wsUrls.length}):`, wsUrl);

    try {
      // Create a new WebSocket connection
      socket = new WebSocket(wsUrl);
      console.log('WebSocket object created, readyState:', socket.readyState);

      // Set a connection timeout
      const connectionTimeout = setTimeout(() => {
        if (socket && socket.readyState !== WebSocket.OPEN) {
          console.log(`Connection timeout for ${wsUrl}`);
          try {
            socket.close();
          } catch (e) {
            console.error('Error closing socket after timeout:', e);
          }
          tryConnect(index + 1);
        }
      }, 5000);

      // Store the timeout so we can clear it if needed
      window.tiptopConnectionTimeout = connectionTimeout;

      socket.onopen = () => {
        console.log('TipTop WebSocket connected to', wsUrl);
        clearTimeout(connectionTimeout);
        window.tiptopConnectionTimeout = null;
        isConnected = true;
        reconnectAttempts = 0;

        // Add current user to active users immediately
        if (!activeUsers) {
          activeUsers = [];
        }

        // Check if current user is already in the list
        const currentUserIndex = activeUsers.findIndex(user => user.userId === userId);
        if (currentUserIndex === -1) {
          // Add current user to the list
          activeUsers.push({
            userId: userId,
            userName: userName,
            lastSeen: new Date().getTime(),
            status: 'online'
          });
        }

        // Update UI immediately to show connected status
        updateSocialUI();

        // Update user list UI immediately
        updateActiveUsersUI();

        // Log connection success with details
        console.log('WebSocket connection established with details:', {
          url: wsUrl,
          userId,
          userName,
          pageUrl: window.location.href
        });

        // Send presence information with a small delay to ensure socket is fully ready
        setTimeout(() => {
          if (socket && socket.readyState === WebSocket.OPEN) {
            console.log('Sending presence information to server');
            socket.send(JSON.stringify({
              type: 'presence',
              userId: userId,
              userName: userName,
              url: window.location.href,
              timestamp: new Date().toISOString()
            }));

            // Request users list immediately after sending presence
            console.log('Requesting users list from server');
            socket.send(JSON.stringify({
              type: 'get_users',
              url: window.location.href
            }));

            // SIMPLIFIED: Request chat history from server (single source of truth)
            console.log('Requesting chat history from server');
            requestChatHistory();
          } else {
            console.error('Socket not open when trying to send presence information');
          }
        }, 100);

        // If we don't receive a welcome message or users list within 2 seconds,
        // try requesting users list again with multiple retries
        [2000, 4000, 6000].forEach(delay => {
          setTimeout(() => {
            if ((activeUsers.length <= 1 || activeUsers.every(user => user.userId === userId)) &&
                socket && socket.readyState === WebSocket.OPEN) {
              console.log(`${delay}ms passed: No other users received, requesting users list again`);
              socket.send(JSON.stringify({
                type: 'get_users',
                url: window.location.href
              }));

              // Update UI to show we're still trying
              updateActiveUsersUI();

              // Log current state
              console.log('Current state after retry request:', {
                activeUsers: activeUsers.map(u => ({ userId: u.userId, userName: u.userName })),
                socketState: socket.readyState
              });
            }
          }, delay);
        });
      };

      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Received WebSocket message:', data);

          // Handle different message types
          switch (data.type) {
            case 'chat':
              // SIMPLIFIED: Handle real-time messages directly
              handleServerMessage(data);
              break;
            case 'presence':
              handlePresenceMessage(data);
              break;
            case 'note':
              handleNoteMessage(data);
              break;
            case 'system':
              console.log('System message:', data.content);
              break;
            case 'history':
              if (data.messages && Array.isArray(data.messages)) {
                console.log(`Received ${data.messages.length} history messages from server`);
                handleServerHistory(data.messages);
                isLoadingHistory = false;

                // Scroll to bottom after processing all history messages
                if (messagesAdded) {
                  // First try regular scroll
                  scrollChatToBottom(true);

                  // Then use the dedicated function for more aggressive scrolling
                  setTimeout(() => {
                    forceScrollChatOnReopen();
                  }, 200);
                }
              }
              break;
            case 'users':
              console.log('Received users list from server:', data);
              if (data.users && Array.isArray(data.users)) {
                console.log(`Processing users list with ${data.users.length} users`);
                updateActiveUsers(data.users);

                // Log the updated active users list
                console.log('Active users after update:', activeUsers.map(u => ({
                  userId: u.userId,
                  userName: u.userName,
                  status: u.status
                })));

                // If we only have the current user, try requesting the list again after a delay
                if (activeUsers.length <= 1 || activeUsers.every(user => user.userId === userId)) {
                  console.log('Still only have current user after users update, will retry in 2s');
                  setTimeout(() => {
                    if (socket && socket.readyState === WebSocket.OPEN) {
                      console.log('Retrying get_users request after receiving empty list');
                      socket.send(JSON.stringify({
                        type: 'get_users',
                        url: window.location.href
                      }));
                    }
                  }, 2000);
                }
              } else {
                console.error('Received invalid users list:', data);
              }
              break;

            case 'scroll_chat':
              // Extract additional information from the enhanced scroll command
              const forceScroll = data.forceScroll === true;
              const senderId = data.senderId || 'unknown';
              const messageId = data.messageId || null;
              const attempt = data.attempt || 1;
              const totalAttempts = data.totalAttempts || 1;

              console.log(`📜 Received scroll command from server (${attempt}/${totalAttempts}): senderId=${senderId}, messageId=${messageId}`);

              // Get the chat log element
              const chatLogElement = document.getElementById('tiptop-chat-log');
              if (!chatLogElement) {
                console.error('❌ Cannot scroll: Chat log element not found');
                break;
              }

              // Direct scroll attempt first
              chatLogElement.scrollTop = chatLogElement.scrollHeight;

              // If this is the first attempt, use our aggressive scroll function
              if (attempt === 1) {
                console.log('🔄 First scroll attempt - using aggressive scroll');
                scrollChatToBottom(true); // Force scroll to ensure visibility
              }
              // For middle attempts, try scrollIntoView on the last message
              else if (attempt < totalAttempts) {
                console.log(`🔄 Scroll attempt ${attempt} - trying scrollIntoView`);
                const messages = chatLogElement.querySelectorAll('.tiptop-chat-message');
                if (messages && messages.length > 0) {
                  const lastMessage = messages[messages.length - 1];
                  lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
                }
              }
              // For the last attempt, try both methods
              else if (attempt === totalAttempts) {
                console.log('🔄 Final scroll attempt - using both methods');

                // First try direct scroll
                chatLogElement.scrollTop = chatLogElement.scrollHeight;

                // Then try scrollIntoView with a small delay
                setTimeout(() => {
                  const messages = chatLogElement.querySelectorAll('.tiptop-chat-message');
                  if (messages && messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });

                    // Log the final scroll position
                    console.log('📏 Final scroll position:', {
                      scrollHeight: chatLogElement.scrollHeight,
                      scrollTop: chatLogElement.scrollTop,
                      clientHeight: chatLogElement.clientHeight,
                      difference: chatLogElement.scrollHeight - chatLogElement.scrollTop - chatLogElement.clientHeight
                    });
                  }
                }, 50);
              }
              break;
            case 'welcome':
              // Handle welcome message with initial user list
              if (data.users) {
                handleWelcomeMessage(data);
              }
              break;
            case 'user_joined':
              // Handle new user joined
              if (data.userId && data.name) {
                addUser(data.userId, data.name);
              }
              break;
            case 'user_left':
              // Handle user left
              if (data.userId) {
                removeUser(data.userId);
              }
              break;
            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      socket.onclose = (event) => {
        console.log('TipTop WebSocket disconnected:', event.code, event.reason);
        clearTimeout(connectionTimeout);

        // If we were previously connected, try to reconnect
        if (isConnected) {
          isConnected = false;
          updateSocialUI();

          // Attempt to reconnect with exponential backoff
          if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
            reconnectAttempts++;
            console.log(`Reconnecting in ${delay}ms (attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
            setTimeout(connectWebSocket, delay);
          } else {
            console.log('Maximum reconnection attempts reached');
            createMockWebSocket();
          }
        } else {
          // If we were never connected, try the next URL
          tryConnect(index + 1);
        }
      };

      socket.onerror = (error) => {
        console.error('TipTop WebSocket error:', error);
        // Don't try next URL here, let onclose handle it
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      tryConnect(index + 1);
    }
  }

  // Create a mock WebSocket for demo/testing purposes
  function createMockWebSocket() {
    console.log('Creating mock WebSocket for demo purposes');
    useMockWebSocket = true;

    // Create a dummy socket object to prevent null reference errors
    socket = {
      readyState: WebSocket.OPEN,
      send: function(data) {
        console.log('Mock socket send:', data);

        // In mock mode, we don't need to echo back the message
        // since we're already adding it to the UI directly in sendChatMessage
        try {
          const messageObj = JSON.parse(data);
          console.log('Mock socket processed message:', messageObj);
          // We don't need to do anything here since the message is already displayed
        } catch (e) {
          console.error('Error parsing mock message:', e);
        }

        return true;
      },
      close: function() {
        console.log('Mock socket close');
        isConnected = false;
      }
    };

    // Simulate connected state
    isConnected = true;
    updateSocialUI();

    // Simulate welcome message with empty user list
    setTimeout(() => {
      handleWelcomeMessage({
        type: 'welcome',
        users: []
      });
    }, 500);
  }
}

// Disconnect from WebSocket server
function disconnectWebSocket() {
  console.log('Disconnecting from WebSocket server');

  // Clear any connection timeout that might be active
  if (window.tiptopConnectionTimeout) {
    clearTimeout(window.tiptopConnectionTimeout);
    window.tiptopConnectionTimeout = null;
  }

  // Clear any retry timeouts that might be active
  if (window.tiptopRetryTimeouts) {
    window.tiptopRetryTimeouts.forEach(timeout => clearTimeout(timeout));
    window.tiptopRetryTimeouts = [];
  }

  if (socket) {
    try {
      // Log the current socket state
      console.log('Socket state before disconnection:', {
        readyState: socket.readyState,
        isConnected
      });

      // Check the socket state
      if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
        // Only try to close if it's open or connecting
        console.log('Closing WebSocket connection');

        // Remove event listeners to prevent any callbacks during closing
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;

        // Close the socket
        socket.close();
      }
    } catch (e) {
      console.error('Error closing WebSocket:', e);
    }

    // Clear the socket reference
    socket = null;
    console.log('Socket reference cleared');
  } else {
    console.log('No active socket to disconnect');
  }

  // Update connection state
  isConnected = false;
  console.log('Connection state updated: isConnected =', isConnected);

  // Update the UI
  updateSocialUI();
}

// Enhanced chat message handler with unified processing
function handleChatMessage(data) {
  console.log('Processing chat message:', {
    messageId: data.messageId,
    userId: data.userId,
    timestamp: data.timestamp,
    serverTimestamp: data.serverTimestamp,
    sequence: data.sequence,
    isFromHistory: data.isFromHistory,
    source: data.source
  });

  // Skip if message doesn't have content
  if (!data.content) {
    console.error('Chat message missing content:', data);
    return;
  }

  // Check if this is our own message coming back from the server
  const isOwnMessage = data.userId === userId;

  // Check if this is a history message (from the history array)
  const isHistoryMessage = data.isFromHistory === true;

  // Skip our own messages coming from the server in real-time (we already added them when sending)
  // But DO NOT skip our own messages when loading history
  if (isOwnMessage && !isHistoryMessage && data.source !== 'storage' && data.source !== 'storage_change') {
    console.log('Skipping own message from server (not history):', data.messageId);
    return;
  }

  // Skip messages we've already processed
  if (processedMessageIds.has(data.messageId)) {
    console.log('Skipping already processed message:', data.messageId);
    return;
  }

  // Make sure we have all required fields, use defaults if not
  const senderId = data.userId || 'unknown';
  const senderName = data.userName || 'Unknown User';
  // Use server timestamp if available, otherwise client timestamp
  const timestamp = data.serverTimestamp || data.timestamp || new Date().toISOString();
  const messageId = data.messageId || `server_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  console.log('Adding chat message to UI:', {
    senderId,
    senderName,
    content: data.content,
    isOwnMessage,
    messageId,
    timestamp,
    serverTimestamp: data.serverTimestamp,
    sequence: data.sequence,
    isFromHistory: isHistoryMessage,
    source: data.source
  });

  // Add the message to the UI with server timestamp for proper ordering
  addChatMessage(senderId, senderName, data.content, timestamp, isOwnMessage, messageId);

  // Save message as note for history (but not if it's already from history to avoid duplication)
  if (!isHistoryMessage) {
    saveMessageAsNote(senderId, senderName, data.content, timestamp, messageId);
  }
}

// Handle presence message from WebSocket server
function handlePresenceMessage(data) {
  console.log('Processing presence message:', data);

  // Skip our own presence messages
  if (data.userId === userId) {
    return;
  }

  // Add user to the list if not already there
  addUser(data.userId, data.userName);
}

// Handle incoming WebSocket messages (legacy handler)
function handleWebSocketMessage(data) {
  console.log('Received WebSocket message (legacy handler):', data);

  // Validate the message has required fields
  if (!data || !data.type) {
    console.error('Invalid WebSocket message received:', data);
    return;
  }

  switch (data.type) {
    case 'welcome':
      // Handle welcome message with initial user list
      if (data.users) {
        handleWelcomeMessage(data);
      } else {
        console.error('Welcome message missing users array:', data);
      }
      break;

    case 'user_joined':
      // Handle new user joined
      if (data.userId && data.name) {
        addUser(data.userId, data.name);
      } else {
        console.error('User joined message missing required fields:', data);
      }
      break;

    case 'user_left':
      // Handle user left
      if (data.userId) {
        removeUser(data.userId);
      } else {
        console.error('User left message missing userId:', data);
      }
      break;

    case 'chat':
      // Handle chat message
      handleChatMessage(data);
      break;

    case 'presence_update':
      // Handle user presence update
      if (data.userId && data.updates) {
        updateUserPresence(data.userId, data.updates);
      } else {
        console.error('Presence update missing required fields:', data);
      }
      break;

    case 'note':
      // Handle note message
      if (data.content) {
        handleNoteMessage(data);
      } else {
        console.error('Note message missing content:', data);
      }
      break;

    case 'error':
      // Handle error message
      if (data.message) {
        showNotification(data.message, 'error');
      } else {
        showNotification('An unknown error occurred', 'error');
      }
      break;

    default:
      console.log('Unknown message type:', data.type);
  }
}

// Handle welcome message with initial user list
function handleWelcomeMessage(data) {
  // Skip if social features are disabled
  if (!socialEnabled) {
    console.log('Social features disabled, ignoring welcome message');
    return;
  }

  console.log('Processing welcome message:', data);

  // Clear existing user list
  const userListElement = document.getElementById('tiptop-user-list');
  if (userListElement) {
    userListElement.innerHTML = '';
  }

  // Make sure we have a valid users array
  const users = Array.isArray(data.users) ? data.users : [];

  console.log(`Welcome message contains ${users.length} users`);

  // If we received an empty user list, request it again
  if (users.length === 0 && socket && socket.readyState === WebSocket.OPEN) {
    console.log('Received empty user list in welcome message, requesting again');
    setTimeout(() => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
          type: 'get_users',
          url: window.location.href
        }));
      }
    }, 1000);
  }

  // Process the users we received
  if (users.length > 0) {
    // Create a new array for active users
    const newActiveUsers = [];

    // Add each user to the list
    users.forEach(user => {
      // Check if user has required fields
      if (user && user.userId) {
        // Use name or userName depending on what's available
        const userName = user.name || user.userName || 'Unknown User';

        // Add to new active users array
        newActiveUsers.push({
          userId: user.userId,
          userName: userName,
          lastSeen: new Date().getTime(),
          status: 'online'
        });

        console.log(`Added user from welcome message: ${userName} (${user.userId})`);
      }
    });

    // Make sure current user is in the list
    const currentUserIndex = newActiveUsers.findIndex(user => user.userId === userId);
    if (currentUserIndex === -1 && userId && userName) {
      // Add current user to the list
      newActiveUsers.push({
        userId: userId,
        userName: userName,
        lastSeen: new Date().getTime(),
        status: 'online'
      });
      console.log(`Added current user to welcome message users: ${userName} (${userId})`);
    }

    // Update the active users array
    activeUsers = newActiveUsers;

    // Update the UI
    updateActiveUsersUI();
  } else {
    // Make sure current user is in the list
    if (userId && userName) {
      // Reset active users to just the current user
      activeUsers = [{
        userId: userId,
        userName: userName,
        lastSeen: new Date().getTime(),
        status: 'online'
      }];

      // Update the UI
      updateActiveUsersUI();
    }
  }

  // Update UI to show connected status
  updateSocialUI();

  // Show welcome notification only if we're connected and social features are enabled
  if (isConnected && socialEnabled) {
    if (users.length > 0) {
      showNotification(`Connected! ${users.length} other users are viewing this page`);
    } else {
      showNotification('Connected! You are the only one viewing this page');
    }
  }
}

// Function to store a message in shared storage for cross-browser communication
function storeSharedMessage(message, senderId, senderName, timestamp, messageId) {
  // Get the current URL as the key for this page's messages
  const currentUrl = window.location.href;

  // Create a URL-safe key by encoding the URL
  // This helps avoid issues with special characters in URLs
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

  // Create the message object
  const messageObj = {
    type: 'chat',
    content: message,
    userId: senderId,
    userName: senderName,
    timestamp: timestamp,
    messageId: messageId,
    url: currentUrl,
    storageTimestamp: Date.now() // Add a timestamp for when it was stored
  };

  console.log('Storing message in shared storage:', messageObj);

  // Store in Chrome storage.sync for cross-browser communication
  chrome.storage.sync.get([`tiptop_msgs_${urlKey}`], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing sync storage:', chrome.runtime.lastError);
      // Fall back to local storage if sync fails
      storeMessageLocally(messageObj, urlKey);
      return;
    }

    let urlMessages = result[`tiptop_msgs_${urlKey}`] || [];

    // Check if this message already exists (by ID)
    const existingIndex = urlMessages.findIndex(m => m.messageId === messageId);
    if (existingIndex >= 0) {
      console.log('Message already exists in storage, not adding again');
      return;
    }

    // Add the new message
    urlMessages.push(messageObj);

    // Limit to 20 messages per URL to prevent storage quota issues
    // sync storage has stricter limits than local storage
    if (urlMessages.length > 20) {
      urlMessages = urlMessages.slice(-20);
    }

    // Save back to storage
    const storageData = {};
    storageData[`tiptop_msgs_${urlKey}`] = urlMessages;

    chrome.storage.sync.set(storageData, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving to sync storage:', chrome.runtime.lastError);
        // Fall back to local storage if sync fails
        storeMessageLocally(messageObj, urlKey);
        return;
      }
      console.log('Message saved to shared sync storage successfully');

      // Also save a separate timestamp entry to force a storage change event
      // This helps ensure other instances detect the change
      const storageKey = `tiptop_ts_${Date.now()}`;
      const timestampData = {};
      timestampData[storageKey] = {
        messageId: messageId,
        urlKey: urlKey
      };
      chrome.storage.sync.set(timestampData);
    });
  });
}

// Fallback function to store message in local storage if sync fails
function storeMessageLocally(messageObj, urlKey) {
  console.log('Falling back to local storage for message:', messageObj);

  chrome.storage.local.get([`tiptop_msgs_${urlKey}`], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing local storage:', chrome.runtime.lastError);
      return;
    }

    let urlMessages = result[`tiptop_msgs_${urlKey}`] || [];

    // Check if this message already exists (by ID)
    const existingIndex = urlMessages.findIndex(m => m.messageId === messageObj.messageId);
    if (existingIndex >= 0) {
      console.log('Message already exists in local storage, not adding again');
      return;
    }

    // Add the new message
    urlMessages.push(messageObj);

    // Limit to 50 messages per URL to prevent storage issues
    if (urlMessages.length > 50) {
      urlMessages = urlMessages.slice(-50);
    }

    // Save back to storage
    const storageData = {};
    storageData[`tiptop_msgs_${urlKey}`] = urlMessages;

    chrome.storage.local.set(storageData, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving to local storage:', chrome.runtime.lastError);
        return;
      }
      console.log('Message saved to local storage successfully');
    });
  });
}

// Set up a listener for storage changes to detect messages from other browser instances
function setupStorageChangeListener() {
  // Create a URL-safe key for the current URL
  const currentUrl = window.location.href;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const messagesKey = `tiptop_msgs_${urlKey}`;

  // Listen for changes in both sync and local storage
  chrome.storage.onChanged.addListener(function(changes, namespace) {
    console.log(`Storage changes detected in ${namespace} storage:`, changes);

    // Check if we have a timestamp entry change (which indicates a new message)
    let timestampChanged = false;
    let changedUrlKey = null;

    Object.keys(changes).forEach(key => {
      // Check for timestamp entries
      if (key.startsWith('tiptop_ts_')) {
        timestampChanged = true;
        // Get the URL key from the timestamp entry
        if (changes[key].newValue && changes[key].newValue.urlKey) {
          changedUrlKey = changes[key].newValue.urlKey;
        }
      }

      // Check for direct message changes for our URL
      if (key === messagesKey) {
        console.log(`Direct change detected for our URL messages in ${namespace} storage`);
        processMessagesFromChange(changes[key].newValue || []);
      }
    });

    // If we detected a timestamp change for our URL, fetch the messages
    if (timestampChanged && changedUrlKey === urlKey) {
      console.log(`Timestamp change detected for our URL in ${namespace} storage`);
      fetchAndProcessMessages(namespace);
    }
    // If we detected a timestamp change but couldn't determine the URL, check both storages
    else if (timestampChanged && !changedUrlKey) {
      console.log(`Timestamp change detected but URL unknown, checking both storages`);
      fetchAndProcessMessages('sync');
      fetchAndProcessMessages('local');
    }
  });

  console.log('Storage change listeners set up for both sync and local storage');

  // Function to fetch and process messages from storage
  function fetchAndProcessMessages(storageType) {
    const storage = storageType === 'sync' ? chrome.storage.sync : chrome.storage.local;

    storage.get([messagesKey], function(result) {
      if (chrome.runtime.lastError) {
        console.error(`Error accessing ${storageType} storage:`, chrome.runtime.lastError);
        return;
      }

      const urlMessages = result[messagesKey] || [];
      console.log(`Found ${urlMessages.length} messages for current URL in ${storageType} storage`);

      processMessagesFromChange(urlMessages);
    });
  }

  // Enhanced processing for storage changes
  function processMessagesFromChange(messages) {
    if (!Array.isArray(messages)) {
      console.error('Expected messages to be an array, got:', typeof messages);
      return;
    }

    console.log(`Processing ${messages.length} messages from storage change`);
    let newMessagesAdded = false;

    messages.forEach(msg => {
      // Skip messages we've already processed
      if (processedMessageIds.has(msg.messageId)) {
        return;
      }

      console.log('Found new message to process from storage change:', msg);
      newMessagesAdded = true;

      // Mark the message as coming from storage change
      msg.isFromHistory = true;
      msg.source = 'storage_change';

      // Add to message queue for unified processing
      messageQueue.set(msg.messageId, msg);
    });

    if (newMessagesAdded) {
      // Process the message queue with proper ordering
      processMessageQueue();
    }

    // If we added any new messages, make sure to scroll to the bottom
    if (newMessagesAdded) {
      // Use multiple scroll attempts with increasing delays for reliability
      [0, 50, 200].forEach(delay => {
        setTimeout(() => {
          scrollChatToBottom(true); // Force scroll to ensure visibility
        }, delay);
      });

      // Also use the dedicated function for more aggressive scrolling
      setTimeout(() => {
        forceScrollChatOnReopen();
      }, 300);
    }
  }

  // Also set up a periodic check as a backup
  // This helps catch messages that might have been missed due to timing issues
  setInterval(function() {
    // Check sync storage first
    chrome.storage.sync.get([messagesKey], function(result) {
      if (chrome.runtime.lastError) {
        console.error('Error accessing sync storage:', chrome.runtime.lastError);
        // If sync fails, fall back to local
        checkLocalStorage();
        return;
      }

      const urlMessages = result[messagesKey] || [];
      console.log(`Periodic check: Found ${urlMessages.length} messages in sync storage`);

      processMessagesFromChange(urlMessages);

      // Also check local storage as a backup
      checkLocalStorage();
    });

    function checkLocalStorage() {
      chrome.storage.local.get([messagesKey], function(result) {
        if (chrome.runtime.lastError) {
          console.error('Error accessing local storage:', chrome.runtime.lastError);
          return;
        }

        const urlMessages = result[messagesKey] || [];
        console.log(`Periodic check: Found ${urlMessages.length} messages in local storage`);

        processMessagesFromChange(urlMessages);
      });
    }
  }, 3000); // Check every 3 seconds as a backup (reduced from 5 seconds for faster sync)
}

// Send a chat message
function sendChatMessage(message) {
  console.log('sendChatMessage called with message:', message);

  // Check if message is empty
  if (!message || message.trim() === '') {
    console.log('Message is empty, not sending');
    return false;
  }

  // Check WebSocket connection
  if (!socket) {
    console.error('WebSocket connection not initialized');
    showNotification('Chat not connected. Please try again later.', 'error');
    return false;
  }

  console.log('WebSocket readyState:', socket.readyState);

  // Handle different connection states
  if (socket.readyState !== WebSocket.OPEN) {
    if (socket.readyState === WebSocket.CONNECTING) {
      console.log('WebSocket is still connecting, waiting...');
      showNotification('Chat is connecting. Please try again in a moment.', 'info');
      return false;
    } else if (socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING) {
      console.log('WebSocket is closed or closing, attempting to reconnect...');
      // Try to reconnect
      connectWebSocket();
      showNotification('Chat reconnecting. Please try again in a moment.', 'info');
      return false;
    }
  }

  // Generate a unique message ID
  const messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const timestamp = new Date().toISOString();

  // Create the message object
  const messageObj = {
    type: 'chat',
    content: message,
    userId: userId,
    userName: userName,
    messageId: messageId,
    timestamp: timestamp,
    url: window.location.href
  };

  console.log('Message object created:', messageObj);

  // Handle mock mode
  if (useMockWebSocket) {
    console.log('Using mock WebSocket mode');

    try {
      console.log('Sending chat message in mock mode:', message);

      // First, add the message to the UI immediately (optimistic UI update)
      addChatMessage(userId, userName, message, timestamp, true, messageId);
      console.log('Message added to UI');

      // SIMPLIFIED: No storage - server is single source of truth
      console.log('Mock mode: message will be handled by server');

      // Then send the message to the mock socket
      socket.send(JSON.stringify(messageObj));
      console.log('Message sent to mock socket');

      // Force scroll to bottom immediately for the sender
      scrollChatToBottom();

      return true;
    } catch (error) {
      console.error('Error sending chat message in mock mode:', error);
      showNotification('Failed to send message: ' + error.message, 'error');
      return false;
    }
  }

  try {
    console.log('Sending WebSocket message:', messageObj);

    // Add the message to the UI immediately (optimistic UI update)
    addChatMessage(userId, userName, message, timestamp, true, messageId);
    console.log('Message added to UI');

    // SIMPLIFIED: Add to displayed set to avoid duplication
    displayedMessageIds.add(messageId);
    console.log('Added message ID to displayed set:', messageId);

    // Send message to WebSocket server
    socket.send(JSON.stringify(messageObj));
    console.log('Message sent to WebSocket server');

    // Force scroll to bottom immediately for the sender
    forceScrollToBottom();

    // Request a scroll command to be sent to all clients
    // Include the message ID so the server can associate the scroll with a specific message
    setTimeout(() => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
          type: 'request_scroll',
          url: window.location.href,
          timestamp: new Date().toISOString(),
          messageId: messageId // Include the message ID for better tracking
        }));
        console.log('Requested scroll command for all clients with messageId:', messageId);
      }
    }, 250); // Increased delay to ensure message is processed first

    // Also save locally as backup
    saveMessageAsNote(userId, userName, message, timestamp, messageId);
    console.log('Message saved as note for history');

    return true;
  } catch (error) {
    console.error('Error sending chat message:', error);
    showNotification('Failed to send message: ' + error.message, 'error');
    return false;
  }
}

// Update the active users list from WebSocket server
function updateActiveUsers(users) {
  console.log('Updating active users list from server:', users);

  if (!Array.isArray(users)) {
    console.error('Invalid users array:', users);
    return;
  }

  // Skip update if social features are disabled
  if (!socialEnabled) {
    console.log('Social features disabled, skipping user list update');
    return;
  }

  // Create a new array for active users
  const newActiveUsers = [];

  // Process each user from the server
  users.forEach(user => {
    // Validate user object
    if (!user || !user.userId) {
      console.log('Skipping invalid user object:', user);
      return;
    }

    // Add user to the new list
    newActiveUsers.push({
      userId: user.userId,
      userName: user.userName || 'Unknown User',
      lastSeen: new Date().getTime(),
      status: 'online' // All users in the list are online
    });
  });

  // Make sure current user is in the list
  const currentUserIndex = newActiveUsers.findIndex(user => user.userId === userId);
  if (currentUserIndex === -1 && userId && userName) {
    // Add current user to the list
    newActiveUsers.push({
      userId: userId,
      userName: userName,
      lastSeen: new Date().getTime(),
      status: 'online'
    });
    console.log('Added current user to active users list:', userId, userName);
  }

  // Log the changes
  console.log('User list update:', {
    previousCount: activeUsers.length,
    newCount: newActiveUsers.length,
    currentUserIncluded: newActiveUsers.some(user => user.userId === userId)
  });

  // Update the active users array
  activeUsers = newActiveUsers;

  // Update UI
  updateActiveUsersUI();
}

// Update the active users UI
function updateActiveUsersUI() {
  const userListElement = document.getElementById('tiptop-user-list');
  if (!userListElement) return;

  // Skip if social features are disabled
  if (!socialEnabled) {
    userListElement.innerHTML = '';
    return;
  }

  // Clear existing users
  userListElement.innerHTML = '';

  // Make sure we have an active users array
  if (!activeUsers) {
    activeUsers = [];
  }

  // Make sure current user is in the list
  const currentUserIndex = activeUsers.findIndex(user => user.userId === userId);
  if (currentUserIndex === -1 && userId && userName) {
    // Add current user to the list
    activeUsers.push({
      userId: userId,
      userName: userName,
      lastSeen: new Date().getTime(),
      status: 'online'
    });
    console.log('Added current user to active users list:', userId, userName);
  }

  // If still no active users, show message
  if (activeUsers.length === 0) {
    userListElement.innerHTML = '<div class="tiptop-no-users">No other users viewing this page</div>';
    return;
  }

  // Sort users to ensure current user is first, then sort by name
  const sortedUsers = [...activeUsers].sort((a, b) => {
    // Current user always comes first
    if (a.userId === userId) return -1;
    if (b.userId === userId) return 1;
    // Otherwise sort alphabetically by name
    return a.userName.localeCompare(b.userName);
  });

  // Add each user to the list with display limits
  sortedUsers.forEach((user, index) => {
    // Skip if user doesn't have required fields
    if (!user.userId || !user.userName) {
      console.log('Skipping user with missing fields:', user);
      return;
    }

    // Check if this is the current user
    const isCurrentUser = user.userId === userId;

    // Generate a random color for the avatar
    const avatarColor = window.TipTopRandomNames ? window.TipTopRandomNames.getRandomColor() : '#3498db';

    // Get the first letter or emoji for the avatar
    let avatarContent = user.userName.charAt(0).toUpperCase();

    // If the name has a space (like "Curious Einstein"), use both initials
    if (user.userName.includes(' ')) {
      const nameParts = user.userName.split(' ');
      if (nameParts.length >= 2) {
        avatarContent = nameParts[0].charAt(0) + nameParts[1].charAt(0);
        avatarContent = avatarContent.toUpperCase();
      }
    }

    const userElement = document.createElement('div');
    userElement.className = 'tiptop-user';
    userElement.dataset.userId = user.userId;

    // Apply faded class to the 3rd user
    if (index === 2) {
      userElement.classList.add('faded');
    }

    // Add status indicator
    const statusClass = user.status === 'online' ? 'tiptop-status-online' : 'tiptop-status-offline';

    // Add user name with (You) for current user
    const displayName = isCurrentUser ? `${escapeHtml(user.userName)} (You)` : escapeHtml(user.userName);

    // Create the HTML with Slack-like availability badge
    userElement.innerHTML = `
      <div class="tiptop-user-avatar" style="background-color: ${avatarColor};">
        ${avatarContent}
        <span class="tiptop-user-status ${statusClass}"></span>
      </div>
      <div class="tiptop-user-name ${isCurrentUser ? 'editable' : ''}" ${isCurrentUser ? 'contenteditable="false"' : ''}>${displayName}</div>
    `;

    // Add click handler for inline editing if it's the current user
    if (isCurrentUser) {
      const nameElement = userElement.querySelector('.tiptop-user-name');
      nameElement.addEventListener('click', function() {
        enableInlineNameEdit(nameElement, user.userName);
      });
    }

    userListElement.appendChild(userElement);
  });

  // If we only have the current user, show a message
  if (activeUsers.length === 1 && activeUsers[0].userId === userId) {
    const messageElement = document.createElement('div');
    messageElement.className = 'tiptop-no-users';
    messageElement.style.marginTop = '10px';
    messageElement.textContent = 'No other users viewing this page';
    userListElement.appendChild(messageElement);
  }
}

// Update the social UI based on connection status
function updateSocialUI() {
  const collaborationSection = document.getElementById('tiptop-collaboration');
  if (!collaborationSection) return;

  const statusElement = collaborationSection.querySelector('.tiptop-collaboration-status');
  const collaborationControls = collaborationSection.querySelector('.tiptop-collaboration-controls');
  const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
  const aiContentSections = document.getElementById('tiptop-ai-content-sections');
  const userListElement = document.getElementById('tiptop-user-list');

  // First, check if social features are enabled
  if (!socialEnabled) {
    // Social features are disabled - hide all social UI and show AI content
    if (statusElement) {
      statusElement.style.display = 'none';
    }

    if (collaborationControls) {
      collaborationControls.style.display = 'none';
    }

    if (collaborationLoading) {
      collaborationLoading.style.display = 'none';
    }

    // Clear any notifications
    const notificationElement = document.getElementById('tiptop-collaboration-notification');
    if (notificationElement) {
      notificationElement.style.display = 'none';
    }

    // Show AI content sections
    if (aiContentSections) {
      aiContentSections.style.display = 'block';
    }

    // Clear user list if it exists
    if (userListElement) {
      userListElement.innerHTML = '';
    }

    return;
  }

  // Social features are enabled - check connection status
  if (isConnected) {
    // Connected - show controls and hide loading/AI content
    if (statusElement) {
      statusElement.style.display = 'block';
      statusElement.innerHTML = '<span class="tiptop-status-connected">●</span> Connected';
    }

    if (collaborationLoading) {
      collaborationLoading.style.display = 'none';
    }

    if (collaborationControls) {
      collaborationControls.style.display = 'block';
    }

    // Hide AI content sections to give more room to social features
    if (aiContentSections) {
      aiContentSections.style.display = 'none';
    }
  } else if (socket && socket.readyState === WebSocket.CONNECTING) {
    // Connecting - show loading state, hide controls
    if (statusElement) {
      statusElement.style.display = 'block';
      statusElement.innerHTML = '<span class="tiptop-status-connecting">●</span> Connecting...';
    }

    if (collaborationLoading) {
      collaborationLoading.style.display = 'block';
    }

    if (collaborationControls) {
      collaborationControls.style.display = 'none';
    }

    // Hide AI content while connecting to social features
    if (aiContentSections) {
      aiContentSections.style.display = 'none';
    }
  } else {
    // Disconnected or failed to connect - show loading state
    if (statusElement) {
      statusElement.style.display = 'block';
      statusElement.innerHTML = '<span class="tiptop-status-disconnected">●</span> Connecting...';
    }

    if (collaborationLoading) {
      collaborationLoading.style.display = 'block';
    }

    if (collaborationControls) {
      collaborationControls.style.display = 'none';
    }

    // Hide AI content when social features are enabled but not connected
    if (aiContentSections) {
      aiContentSections.style.display = 'none';
    }
  }
}

// Add a user to the user list
function addUser(userId, userName) {
  const userListElement = document.getElementById('tiptop-user-list');
  if (!userListElement) return;

  // Remove "no users" message if present
  const noUsersElement = userListElement.querySelector('.tiptop-no-users');
  if (noUsersElement) {
    userListElement.removeChild(noUsersElement);
  }

  // Check if user already exists
  if (userListElement.querySelector(`[data-user-id="${userId}"]`)) {
    return;
  }

  // Check if this is the current user
  const isCurrentUser = userId === self.userId;

  // Generate a random color for the avatar
  const avatarColor = window.TipTopRandomNames ? window.TipTopRandomNames.getRandomColor() : '#3498db';

  // Get the first letter or emoji for the avatar
  let avatarContent = userName.charAt(0).toUpperCase();

  // If the name has a space (like "Curious Einstein"), use both initials
  if (userName.includes(' ')) {
    const nameParts = userName.split(' ');
    if (nameParts.length >= 2) {
      avatarContent = nameParts[0].charAt(0) + nameParts[1].charAt(0);
      avatarContent = avatarContent.toUpperCase();
    }
  }

  // Add user name with (You) for current user
  const displayName = isCurrentUser ? `${escapeHtml(userName)} (You)` : escapeHtml(userName);

  const userElement = document.createElement('div');
  userElement.className = 'tiptop-user';
  userElement.dataset.userId = userId;
  userElement.innerHTML = `
    <div class="tiptop-user-avatar" style="background-color: ${avatarColor};">
      ${avatarContent}
      <span class="tiptop-user-status tiptop-status-online"></span>
    </div>
    <div class="tiptop-user-name">${displayName}</div>
  `;
  userListElement.appendChild(userElement);
}

// Remove a user from the user list
function removeUser(userId) {
  const userListElement = document.getElementById('tiptop-user-list');
  if (!userListElement) return;

  const userElement = userListElement.querySelector(`[data-user-id="${userId}"]`);
  if (userElement) {
    userListElement.removeChild(userElement);
  }

  // Add "no users" message if list is empty
  if (userListElement.children.length === 0) {
    userListElement.innerHTML = '<div class="tiptop-no-users">No other users viewing this page</div>';
  }
}

// Update user presence information
function updateUserPresence(userId, updates) {
  const userElement = document.querySelector(`[data-user-id="${userId}"]`);
  if (!userElement) return;

  // Update user name if provided
  if (updates.name) {
    const nameElement = userElement.querySelector('.tiptop-user-name');
    if (nameElement) {
      nameElement.textContent = escapeHtml(updates.name);
    }
  }

  // Update avatar if provided
  if (updates.avatar) {
    const avatarElement = userElement.querySelector('.tiptop-user-avatar');
    if (avatarElement) {
      avatarElement.textContent = updates.avatar;
    }
  }
}

// SIMPLIFIED: Single source of truth - only server is authoritative
let lastKnownMessageSequence = 0; // Track what we've displayed
let isLoadingHistory = false; // Prevent duplicate history requests
let chatInitialized = false; // Track if chat has been initialized
let displayedMessageIds = new Set(); // Simple deduplication for UI only

// SIMPLIFIED: Clear chat and reload from server
function refreshChatFromServer() {
  console.log('Refreshing chat from server (single source of truth)');

  // Clear the chat UI
  const chatContainer = document.getElementById('tiptop-chat-container');
  if (chatContainer) {
    const chatLog = chatContainer.querySelector('.tiptop-chat-log');
    if (chatLog) {
      chatLog.innerHTML = '';
    }
  }

  // Clear our simple tracking
  displayedMessageIds.clear();
  lastKnownMessageSequence = 0;

  // Request fresh data from server
  requestChatHistory();
}

// SIMPLIFIED: Request chat history from server only
function requestChatHistory() {
  if (isLoadingHistory || !socket || socket.readyState !== WebSocket.OPEN) {
    console.log('Cannot request chat history: already loading or WebSocket not connected');
    return;
  }

  console.log('Requesting chat history from server');
  isLoadingHistory = true;

  const historyRequest = {
    type: 'get_history',
    url: window.location.href,
    timestamp: new Date().toISOString(),
    limit: 100,
    fromSequence: lastKnownMessageSequence
  };

  try {
    socket.send(JSON.stringify(historyRequest));
    console.log('History request sent to server:', historyRequest);
  } catch (error) {
    console.error('Error sending history request:', error);
    isLoadingHistory = false;
  }
}

// Unified message processing function
function processMessageQueue() {
  console.log(`Processing message queue with ${messageQueue.size} messages`);

  // Convert queue to array and sort by timestamp (and sequence if available)
  const queuedMessages = Array.from(messageQueue.values());

  // Sort messages by timestamp, with server timestamp taking priority
  queuedMessages.sort((a, b) => {
    // Use server timestamp if available, otherwise client timestamp
    const timestampA = a.serverTimestamp || a.timestamp;
    const timestampB = b.serverTimestamp || b.timestamp;

    const timeA = new Date(timestampA).getTime();
    const timeB = new Date(timestampB).getTime();

    // If timestamps are equal, use sequence number if available
    if (timeA === timeB) {
      const seqA = a.sequence || 0;
      const seqB = b.sequence || 0;
      return seqA - seqB;
    }

    return timeA - timeB;
  });

  console.log(`Sorted ${queuedMessages.length} messages for processing`);

  // Process messages in order
  queuedMessages.forEach(msg => {
    if (processedMessageIds.has(msg.messageId)) {
      console.log('Skipping already processed message:', msg.messageId);
      messageQueue.delete(msg.messageId);
      return;
    }

    console.log('Processing queued message:', {
      messageId: msg.messageId,
      timestamp: msg.timestamp,
      serverTimestamp: msg.serverTimestamp,
      sequence: msg.sequence,
      source: msg.source
    });

    // Add to message history
    messageHistory.push(msg);

    // For chat messages, use handleChatMessage to ensure consistent processing
    if (msg.type === 'chat' || !msg.type) {
      handleChatMessage({
        type: 'chat',
        userId: msg.userId,
        userName: msg.userName,
        content: msg.content,
        timestamp: msg.timestamp,
        serverTimestamp: msg.serverTimestamp,
        sequence: msg.sequence,
        messageId: msg.messageId,
        isFromHistory: msg.isFromHistory || false,
        source: msg.source
      });
    } else {
      // Add the message to the UI directly for other types
      addChatMessage(
        msg.userId,
        msg.userName,
        msg.content,
        msg.serverTimestamp || msg.timestamp,
        msg.userId === userId, // isOwn
        msg.messageId
      );
    }

    // Remove from queue after processing
    messageQueue.delete(msg.messageId);
  });

  // Keep message history manageable (last 200 messages)
  if (messageHistory.length > 200) {
    messageHistory.splice(0, messageHistory.length - 200);
  }

  console.log(`Message queue processing complete. History now contains ${messageHistory.length} messages`);
}

// Function to load more chat history (for infinite scroll)
function loadMoreChatHistory() {
  if (!socket || socket.readyState !== WebSocket.OPEN) {
    console.log('Cannot load more history: WebSocket not connected');
    return;
  }

  console.log('Requesting more chat history from server');

  // Request more history from the server
  const historyRequest = {
    type: 'get_history',
    timestamp: new Date().toISOString(),
    limit: 100, // Request more messages
    offset: messageHistory.length // Use current history length as offset
  };

  try {
    socket.send(JSON.stringify(historyRequest));
    console.log('History request sent to server');
  } catch (error) {
    console.error('Error sending history request:', error);
  }
}

// Function to setup infinite scroll for chat
function setupChatInfiniteScroll() {
  const chatContainer = document.getElementById('tiptop-chat-container');
  if (!chatContainer) return;

  const chatLog = chatContainer.querySelector('.tiptop-chat-log');
  if (!chatLog) return;

  // Add scroll event listener for infinite scroll
  chatLog.addEventListener('scroll', function() {
    // Check if user scrolled to the top (with some threshold)
    if (chatLog.scrollTop <= 50) {
      console.log('User scrolled to top, loading more history');
      loadMoreChatHistory();
    }
  });

  console.log('Infinite scroll setup for chat history');
}

// Debug function to check message ordering
function debugMessageOrdering() {
  console.log('=== MESSAGE ORDERING DEBUG ===');
  console.log(`Processed message IDs: ${processedMessageIds.size}`);
  console.log(`Message queue: ${messageQueue.size}`);
  console.log(`Message history: ${messageHistory.length}`);
  console.log(`Last processed sequence: ${lastProcessedSequence}`);

  if (messageHistory.length > 0) {
    console.log('Recent message history (last 5):');
    messageHistory.slice(-5).forEach((msg, index) => {
      console.log(`  ${index + 1}. ${msg.messageId} - ${msg.timestamp} (server: ${msg.serverTimestamp}, seq: ${msg.sequence})`);
    });
  }

  if (messageQueue.size > 0) {
    console.log('Pending messages in queue:');
    Array.from(messageQueue.values()).forEach((msg, index) => {
      console.log(`  ${index + 1}. ${msg.messageId} - ${msg.timestamp} (server: ${msg.serverTimestamp}, seq: ${msg.sequence})`);
    });
  }
  console.log('=== END DEBUG ===');
}

// Make debug function available globally for testing
window.debugTipTopMessages = debugMessageOrdering;

// SIMPLIFIED: Handle messages from server history
function handleServerHistory(messages) {
  console.log(`Processing ${messages.length} history messages from server`);

  // Sort messages by sequence number (server authoritative ordering)
  messages.sort((a, b) => {
    const seqA = a.sequence || 0;
    const seqB = b.sequence || 0;
    return seqA - seqB;
  });

  // Add each message to UI if not already displayed
  messages.forEach(msg => {
    if (!displayedMessageIds.has(msg.messageId)) {
      addMessageToUI(msg, true); // true = from history
      displayedMessageIds.add(msg.messageId);

      // Update our sequence tracker
      if (msg.sequence && msg.sequence > lastKnownMessageSequence) {
        lastKnownMessageSequence = msg.sequence;
      }
    }
  });

  console.log(`History processing complete. Last sequence: ${lastKnownMessageSequence}`);
}

// SIMPLIFIED: Handle real-time messages from server
function handleServerMessage(message) {
  console.log('Processing real-time message from server:', message);

  // Skip if already displayed
  if (displayedMessageIds.has(message.messageId)) {
    console.log('Message already displayed, skipping:', message.messageId);
    return;
  }

  // Add to UI
  addMessageToUI(message, false); // false = real-time
  displayedMessageIds.add(message.messageId);

  // Update sequence tracker
  if (message.sequence && message.sequence > lastKnownMessageSequence) {
    lastKnownMessageSequence = message.sequence;
  }
}

// SIMPLIFIED: Add message to UI (single function for all message display)
function addMessageToUI(message, isFromHistory = false) {
  console.log('Adding message to UI:', {
    messageId: message.messageId,
    sequence: message.sequence,
    isFromHistory: isFromHistory,
    content: message.content?.substring(0, 30) + '...'
  });

  // Use server timestamp if available, otherwise client timestamp
  const timestamp = message.serverTimestamp || message.timestamp;

  // Determine if this is our own message
  const isOwnMessage = message.userId === userId;

  // Add to chat UI using existing function
  addChatMessage(
    message.userId,
    message.userName,
    message.content,
    timestamp,
    isOwnMessage,
    message.messageId
  );
}

// Initialize the chat container
function initializeChatContainer() {
  const chatContainer = document.getElementById('tiptop-chat-container');
  if (!chatContainer) {
    console.error('Chat container not found');
    return false;
  }

  // Check if we need to reinitialize the chat container
  // This handles cases where the container shows "Chat will be available once connected"
  // or other placeholder messages that should be replaced
  const needsReinit = chatContainer.innerHTML.includes('Chat will be available once connected') ||
                      chatContainer.innerHTML.includes('Connecting to server') ||
                      chatContainer.innerHTML.includes('tiptop-no-users');

  if (needsReinit) {
    console.log('Reinitializing chat container - clearing existing content');
    chatContainer.innerHTML = '';
  }

  // Create chat log if it doesn't exist
  let chatLogElement = document.getElementById('tiptop-chat-log');
  if (!chatLogElement) {
    chatLogElement = document.createElement('div');
    chatLogElement.id = 'tiptop-chat-log';
    chatLogElement.className = 'tiptop-chat-log';
    chatContainer.appendChild(chatLogElement);
    console.log('Created chat log element');

    // Flag to track that this is a newly created chat log
    // This will help us ensure proper scrolling after initialization
    chatLogElement.dataset.newlyCreated = 'true';

    // Set up a MutationObserver to detect when new messages are added
    // This ensures we scroll to the bottom even if messages are added in ways we don't directly control
    if (window.MutationObserver) {
      // First, add a scroll event listener to detect when user manually scrolls
      chatLogElement.addEventListener('scroll', function() {
        const scrollHeight = chatLogElement.scrollHeight;
        const scrollTop = chatLogElement.scrollTop;
        const clientHeight = chatLogElement.clientHeight;
        const scrollBottom = scrollTop + clientHeight;

        // Determine if user is at the bottom
        const isAtBottom = (scrollHeight - scrollBottom) < 30 || (scrollTop / scrollHeight > 0.9);

        // Update the userHasScrolledUp flag based on scroll position
        if (!isAtBottom && !userHasScrolledUp) {
          userHasScrolledUp = true;
          console.log('User scrolled up manually (detected by scroll event)');
        } else if (isAtBottom && userHasScrolledUp) {
          userHasScrolledUp = false;
          console.log('User scrolled back to bottom (detected by scroll event)');
        }

        // Update last known scroll position
        lastScrollHeight = scrollHeight;
        lastScrollTop = scrollTop;
      });

      // Set up the MutationObserver with improved detection
      const chatObserver = new MutationObserver((mutations) => {
        let newMessagesAdded = false;
        let messageCount = 0;

        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any of the added nodes are chat messages
            for (let i = 0; i < mutation.addedNodes.length; i++) {
              const node = mutation.addedNodes[i];
              if (node.nodeType === 1 && node.classList.contains('tiptop-chat-message')) {
                newMessagesAdded = true;
                messageCount++;
              }
            }
          }
        });

        if (newMessagesAdded) {
          console.log(`MutationObserver detected ${messageCount} new message(s) added`);

          // Use requestAnimationFrame to ensure we scroll after the browser has rendered the new content
          requestAnimationFrame(() => {
            // Scroll to bottom, but respect user's manual scroll position
            scrollChatToBottom();

            // If we have multiple messages added at once, try one more time after a delay
            // This helps with batch updates that might take longer to render
            if (messageCount > 1) {
              setTimeout(() => scrollChatToBottom(), 100);
            }
          });
        }
      });

      // Start observing the chat log for added messages with subtree option
      // This ensures we catch changes to nested elements as well
      chatObserver.observe(chatLogElement, {
        childList: true,
        subtree: true,
        characterData: true  // Also detect text content changes
      });

      console.log('Set up enhanced MutationObserver for chat log');
    }
  }

  return true;
}

// Add a chat message to the chat log
function addChatMessage(senderId, senderName, message, timestamp, isOwn = false, messageId = null) {
  // Initialize the chat container first
  if (!initializeChatContainer()) {
    console.error('Failed to initialize chat container');
    return;
  }

  // Setup infinite scroll if not already done
  setTimeout(() => {
    setupChatInfiniteScroll();
  }, 100);

  const chatContainer = document.getElementById('tiptop-chat-container');

  // Skip empty messages
  if (!message || message.trim() === '') {
    console.log('Skipping empty message');
    return;
  }

  // Generate a consistent message ID if none provided
  if (!messageId) {
    messageId = `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // SIMPLIFIED: Check if already displayed (UI-only deduplication)
  if (displayedMessageIds.has(messageId)) {
    console.log('Skipping duplicate message with ID:', messageId);
    return;
  }

  // Add this message ID to our displayed set
  displayedMessageIds.add(messageId);
  console.log('Added message ID to displayed set:', messageId);

  // Limit the size of the set to prevent memory issues
  if (displayedMessageIds.size > 200) {
    // Remove the oldest entries
    const idsArray = Array.from(displayedMessageIds);
    displayedMessageIds.clear();
    idsArray.slice(-100).forEach(id => displayedMessageIds.add(id));
    console.log('Trimmed displayed message IDs set to 100 entries');
  }

  // Get the chat log element
  let chatLogElement = document.getElementById('tiptop-chat-log');
  if (!chatLogElement) {
    console.error('Chat log element not found even after initialization');
    return;
  }

  // Check if we already have a message with this ID (shouldn't happen, but just in case)
  if (messageId) {
    const existingMessage = chatLogElement.querySelector(`[data-message-id="${messageId}"]`);
    if (existingMessage) {
      console.log('Message with ID already exists in DOM, not adding again:', messageId);
      return;
    }
  }

  const messageElement = document.createElement('div');
  messageElement.className = `tiptop-chat-message ${isOwn ? 'tiptop-own-message' : ''}`;
  if (messageId) {
    messageElement.dataset.messageId = messageId;
  }

  // Format the timestamp
  let time;
  try {
    time = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } catch (e) {
    console.error('Invalid timestamp:', timestamp);
    time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  // Get avatar color and content for the sender
  let avatarColor = '#3498db';
  let avatarContent = 'U'; // Default

  if (senderName && typeof senderName === 'string') {
    avatarContent = senderName.charAt(0).toUpperCase();

    // If the name has a space (like "Curious Einstein"), use both initials
    if (senderName.includes(' ')) {
      const nameParts = senderName.split(' ');
      if (nameParts.length >= 2) {
        avatarContent = nameParts[0].charAt(0) + nameParts[1].charAt(0);
        avatarContent = avatarContent.toUpperCase();
      }
    }
  }

  // Try to get the avatar color from the user list
  if (senderId) {
    const userElement = document.querySelector(`.tiptop-user[data-user-id="${senderId}"]`);
    if (userElement) {
      const avatarElement = userElement.querySelector('.tiptop-user-avatar');
      if (avatarElement && avatarElement.style.backgroundColor) {
        avatarColor = avatarElement.style.backgroundColor;
      }
    }
  }

  // Generate a random color if we're in mock mode and don't have a color
  if (useMockWebSocket && avatarColor === '#3498db' && window.TipTopRandomNames) {
    avatarColor = window.TipTopRandomNames.getRandomColor();
  }

  // Check if message is long (more than 3 lines worth of text)
  const isLongMessage = message.length > 150; // Approximate 3 lines
  const truncatedMessage = isLongMessage ? message.substring(0, 150) + '...' : message;

  messageElement.innerHTML = `
    <div class="tiptop-message-avatar" style="background-color: ${avatarColor};">${avatarContent}</div>
    <div class="tiptop-message-bubble">
      <div class="tiptop-message-sender">${isOwn ? 'You' : escapeHtml(senderName || 'Unknown User')}</div>
      <div class="tiptop-message-content ${isLongMessage ? 'truncated' : ''}" data-full-message="${escapeHtml(message)}">${escapeHtml(isLongMessage ? truncatedMessage : message)}</div>
      ${isLongMessage ? '<div class="tiptop-show-more">... more</div>' : ''}
      <div class="tiptop-message-time">${time}</div>
    </div>
  `;

  // Add click handler for "show more" functionality
  if (isLongMessage) {
    const showMoreElement = messageElement.querySelector('.tiptop-show-more');
    const contentElement = messageElement.querySelector('.tiptop-message-content');

    showMoreElement.addEventListener('click', function() {
      if (contentElement.classList.contains('truncated')) {
        // Show full message
        contentElement.classList.remove('truncated');
        contentElement.textContent = message;
        showMoreElement.textContent = '... less';
      } else {
        // Show truncated message
        contentElement.classList.add('truncated');
        contentElement.textContent = truncatedMessage;
        showMoreElement.textContent = '... more';
      }
    });
  }

  // Add the message to the DOM
  chatLogElement.appendChild(messageElement);

  // Force layout recalculation to ensure scrollHeight is updated
  // This helps browsers properly calculate the new scroll position
  void chatLogElement.offsetHeight;

  console.log('📝 Added new message to chat log, triggering scroll');

  // Check if this is a newly created chat log or if we're reopening the panel
  const isNewlyCreated = chatLogElement.dataset.newlyCreated === 'true';
  const forceScroll = isNewlyCreated || document.getElementById('tiptop-panel').dataset.justReopened === 'true';

  if (forceScroll) {
    console.log('Force scrolling because chat log is newly created or panel was just reopened');
    // Remove the flags after processing
    delete chatLogElement.dataset.newlyCreated;
    if (document.getElementById('tiptop-panel')) {
      delete document.getElementById('tiptop-panel').dataset.justReopened;
    }
  }

  // Scroll to the bottom of the chat log with a direct call
  chatLogElement.scrollTop = chatLogElement.scrollHeight;

  // Use multiple scroll attempts with increasing delays to ensure scrolling works
  // This is more aggressive and reliable than previous approach
  [0, 10, 50, 100, 300].forEach(delay => {
    setTimeout(() => {
      // Direct scrollTop manipulation
      if (chatLogElement) {
        chatLogElement.scrollTop = chatLogElement.scrollHeight;
      }

      // Also try scrollIntoView on the message we just added
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'auto', block: 'end' });
      }

      // Use our enhanced scroll function for the later attempts
      if (delay >= 50 || forceScroll) {
        scrollChatToBottom(true); // Force scroll to ensure visibility
      }
    }, delay);
  });

  // Also save the message as a note for history
  if (messageId && !messageId.startsWith('pending_')) {
    saveMessageAsNote(senderId, senderName, message, timestamp, messageId);
  }
}

// Track if user has manually scrolled up
let userHasScrolledUp = false;
let lastScrollHeight = 0;
let lastScrollTop = 0;
let scrollDebugMode = true; // Enable debug logging for scroll issues

// Function to scroll chat to bottom with aggressive reliability
function scrollChatToBottom(forceScroll = false) {
  // First, find the chat container - try multiple selectors to ensure we get the right element
  const chatLogElement = document.getElementById('tiptop-chat-log');
  const chatContainer = document.getElementById('tiptop-chat-container');

  // If chat log element doesn't exist, try to initialize the chat container first
  if (!chatLogElement) {
    console.log('Chat log element not found, attempting to initialize chat container');

    // Check if we need to initialize the chat container
    if (chatContainer && chatContainer.innerHTML.includes('Chat will be available once connected')) {
      console.log('Found placeholder message, reinitializing chat container');
      initializeChatContainer();

      // Try to get the chat log element again after initialization
      const newChatLogElement = document.getElementById('tiptop-chat-log');
      if (newChatLogElement) {
        console.log('Successfully initialized chat container, proceeding with scroll');
        // Continue with the newly created element
        return scrollChatToBottom(true); // Force scroll with the new element
      }
    }

    console.error('❌ Cannot scroll: Chat log element not found even after initialization attempt');
    return;
  }

  if (scrollDebugMode) {
    console.log('🔍 SCROLL DEBUG: Attempting to scroll chat to bottom');
    console.log('🔍 Chat log element exists:', !!chatLogElement);
    console.log('🔍 Chat container exists:', !!chatContainer);
  }

  // Get current scroll position information
  const scrollHeight = chatLogElement.scrollHeight;
  const scrollTop = chatLogElement.scrollTop;
  const clientHeight = chatLogElement.clientHeight;
  const scrollBottom = scrollTop + clientHeight;

  if (scrollDebugMode) {
    console.log('🔍 SCROLL DEBUG: Current scroll state:', {
      scrollHeight,
      scrollTop,
      clientHeight,
      scrollBottom,
      lastScrollHeight,
      lastScrollTop,
      userHasScrolledUp,
      forceScroll
    });
  }

  // Determine if user is scrolled up (not at bottom)
  // We consider "at bottom" if within 30px of the bottom or if scrolled more than 90% down
  const isAtBottom = (scrollHeight - scrollBottom) < 30 || (scrollTop / scrollHeight > 0.9);

  // If scroll height has changed since last check and user was not at bottom,
  // they probably scrolled up manually to read previous messages
  if (lastScrollHeight > 0 && scrollHeight > lastScrollHeight && !isAtBottom && !forceScroll) {
    userHasScrolledUp = true;
    console.log('👆 User appears to have scrolled up manually, not auto-scrolling');
  }

  // Reset the manual scroll flag if user has scrolled back to bottom themselves
  if (isAtBottom && userHasScrolledUp) {
    userHasScrolledUp = false;
    console.log('👇 User has scrolled back to bottom, resuming auto-scroll');
  }

  // AGGRESSIVE SCROLLING APPROACH:
  // Only auto-scroll if user hasn't manually scrolled up or if force scroll is requested
  if (!userHasScrolledUp || forceScroll) {
    if (scrollDebugMode) {
      console.log('🔍 SCROLL DEBUG: Executing aggressive scroll sequence');
    }

    // 1. First attempt - direct and immediate
    chatLogElement.scrollTop = chatLogElement.scrollHeight;

    // 2. Use requestAnimationFrame for frame-synchronized scrolling
    requestAnimationFrame(() => {
      chatLogElement.scrollTop = chatLogElement.scrollHeight;

      // 3. Also try scrolling the parent container if it exists
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }

      // 4. Multiple delayed attempts with increasing timeouts
      const scrollAttempts = [10, 50, 100, 300, 500];

      scrollAttempts.forEach((delay, index) => {
        setTimeout(() => {
          if (chatLogElement) {
            // Direct DOM manipulation with scroll
            chatLogElement.scrollTop = chatLogElement.scrollHeight;

            // Also try scrollIntoView on the last message
            const messages = chatLogElement.querySelectorAll('.tiptop-chat-message');
            if (messages && messages.length > 0) {
              const lastMessage = messages[messages.length - 1];
              lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
            }

            if (scrollDebugMode) {
              console.log(`🔍 SCROLL DEBUG: Attempt ${index + 1} (${delay}ms) - scrollHeight: ${chatLogElement.scrollHeight}, scrollTop: ${chatLogElement.scrollTop}`);
            }
          }
        }, delay);
      });

      // 5. Final attempt with a longer delay
      setTimeout(() => {
        if (chatLogElement) {
          chatLogElement.scrollTop = chatLogElement.scrollHeight;
          console.log('✅ Final scroll attempt completed');

          // Check if scroll was successful
          const finalScrollTop = chatLogElement.scrollTop;
          const finalScrollHeight = chatLogElement.scrollHeight;
          const finalClientHeight = chatLogElement.clientHeight;

          if (finalScrollHeight - finalScrollTop - finalClientHeight > 5) {
            console.warn('⚠️ Scroll may not have reached bottom. Difference:',
              finalScrollHeight - finalScrollTop - finalClientHeight);
          } else {
            console.log('✅ Scroll successful - at bottom');
          }
        }
      }, 600);
    });
  } else {
    // If we're not scrolling, show a "new message" indicator that user can click to scroll down
    showNewMessageIndicator();
  }

  // Update last known scroll position for next comparison
  lastScrollHeight = scrollHeight;
  lastScrollTop = scrollTop;
}

// Function to show a "new message" indicator when user has scrolled up
function showNewMessageIndicator() {
  // Check if indicator already exists
  let indicator = document.getElementById('tiptop-new-message-indicator');

  if (!indicator) {
    // Create the indicator if it doesn't exist
    indicator = document.createElement('div');
    indicator.id = 'tiptop-new-message-indicator';
    indicator.className = 'tiptop-new-message-indicator';
    indicator.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 19V5M5 12l7-7 7 7"/>
      </svg>
      New messages
    `;

    // Add click handler to scroll to bottom
    indicator.addEventListener('click', () => {
      userHasScrolledUp = false; // Reset the flag
      scrollChatToBottom(true); // Force scroll to bottom
      hideNewMessageIndicator();
    });

    // Add to chat container
    const chatContainer = document.getElementById('tiptop-chat-container');
    if (chatContainer) {
      chatContainer.appendChild(indicator);
    }
  }

  // Make sure it's visible
  indicator.style.display = 'flex';

  // Auto-hide after 5 seconds
  setTimeout(hideNewMessageIndicator, 5000);
}

// Function to hide the new message indicator
function hideNewMessageIndicator() {
  const indicator = document.getElementById('tiptop-new-message-indicator');
  if (indicator) {
    indicator.style.display = 'none';
  }
}

// Show a notification in the collaboration panel
function showNotification(message, type = 'info') {
  const notificationElement = document.getElementById('tiptop-collaboration-notification');
  if (!notificationElement) return;

  notificationElement.textContent = message;
  notificationElement.className = `tiptop-notification tiptop-notification-${type}`;
  notificationElement.style.display = 'block';

  // Hide after 5 seconds
  setTimeout(() => {
    notificationElement.style.display = 'none';
  }, 5000);
}

// Toggle social features on/off
function toggleSocialFeatures(enabled) {
  console.log('Toggling social features:', enabled ? 'ON' : 'OFF');

  // Update the state variable
  socialEnabled = enabled;

  // Save preference to sync storage
  chrome.storage.sync.set({ tiptopSocialEnabled: enabled }, function() {
    if (chrome.runtime.lastError) {
      console.error('Error saving social preference to sync storage:', chrome.runtime.lastError);
      // Fall back to local storage
      chrome.storage.local.set({ tiptopSocialEnabled: enabled });
    }
  });

  // Clear any existing welcome messages or notifications
  const notificationElement = document.getElementById('tiptop-collaboration-notification');
  if (notificationElement) {
    notificationElement.style.display = 'none';
  }

  // Log the current state for debugging
  console.log('Social features toggled. Current state:', {
    socialEnabled,
    isConnected,
    socketState: socket ? socket.readyState : 'no socket',
    activeUsers: activeUsers ? activeUsers.length : 0
  });

  // Clear user list
  const userListElement = document.getElementById('tiptop-user-list');
  if (userListElement) {
    userListElement.innerHTML = '';
  }

  // Clear chat log
  const chatLogElement = document.getElementById('tiptop-chat-log');
  if (chatLogElement) {
    chatLogElement.innerHTML = '';
  }

  if (enabled) {
    // Reset connection state variables
    isConnected = false;
    reconnectAttempts = 0;

    // Clear processed message IDs
    processedMessageIds.clear();

    // Clear active users but keep current user
    if (userId && userName) {
      activeUsers = [{
        userId: userId,
        userName: userName,
        lastSeen: new Date().getTime(),
        status: 'online'
      }];
    } else {
      activeUsers = [];
    }

    // Show connecting status and loading state immediately
    const statusElement = document.querySelector('.tiptop-collaboration-status');
    const collaborationLoading = document.getElementById('tiptop-collaboration-loading');
    const collaborationControls = document.querySelector('.tiptop-collaboration-controls');

    if (statusElement) {
      statusElement.style.display = 'block';
      statusElement.innerHTML = '<span class="tiptop-status-connecting">●</span> Connecting...';
    }

    // Show loading state, hide controls until connected
    if (collaborationLoading) {
      collaborationLoading.style.display = 'block';
    }
    if (collaborationControls) {
      collaborationControls.style.display = 'none';
    }

    // Update user list to show loading state
    updateActiveUsersUI();

    // Connect to WebSocket server with a slight delay to allow UI to update
    setTimeout(() => {
      // Force close any existing connection first
      if (socket) {
        try {
          console.log('Closing existing socket before reconnecting');
          socket.close();
          socket = null;
        } catch (e) {
          console.error('Error closing existing socket:', e);
        }
      }

      // Connect to WebSocket server
      console.log('Initiating new WebSocket connection after toggle');
      connectWebSocket();

      // Clear any existing retry timeouts
      if (window.tiptopRetryTimeouts && window.tiptopRetryTimeouts.length > 0) {
        console.log('Clearing existing retry timeouts:', window.tiptopRetryTimeouts.length);
        window.tiptopRetryTimeouts.forEach(timeout => clearTimeout(timeout));
        window.tiptopRetryTimeouts = [];
      }

      // Set multiple timeouts to retry getting the user list if needed
      const retryIntervals = [1000, 3000, 5000, 10000, 15000]; // 1s, 3s, 5s, 10s, 15s

      retryIntervals.forEach(delay => {
        const timeoutId = setTimeout(() => {
          // Remove this timeout from the tracking array
          const index = window.tiptopRetryTimeouts.indexOf(timeoutId);
          if (index !== -1) {
            window.tiptopRetryTimeouts.splice(index, 1);
          }

          if (isConnected && (activeUsers.length <= 1 || activeUsers.every(user => user.userId === userId))) {
            console.log(`Retry ${delay}ms: Requesting users list from server`);
            // Request users list from server
            if (socket && socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({
                type: 'get_users',
                url: window.location.href
              }));

              // Log current state
              console.log('Current state after requesting users:', {
                isConnected,
                socketState: socket.readyState,
                activeUsers: activeUsers.map(u => ({ userId: u.userId, userName: u.userName }))
              });
            } else {
              console.log('Socket not open, cannot request users list');
              // Try to reconnect if socket is not open
              if (!socket || socket.readyState !== WebSocket.CONNECTING) {
                console.log('Attempting to reconnect WebSocket');
                connectWebSocket();
              }
            }
          } else {
            console.log(`Retry ${delay}ms: Skipped - already have users or not connected`, {
              isConnected,
              activeUsersCount: activeUsers.length,
              socialEnabled
            });
          }
        }, delay);

        // Track this timeout
        window.tiptopRetryTimeouts.push(timeoutId);
      });

      console.log(`Scheduled ${retryIntervals.length} retry attempts for user list updates`);
    }, 100);
  } else {
    // Disconnect from WebSocket server
    console.log('Disabling social features, disconnecting WebSocket');
    disconnectWebSocket();

    // Reset connection state variables
    isConnected = false;
    reconnectAttempts = 0;

    // SIMPLIFIED: Clear simple tracking
    displayedMessageIds.clear();
    lastKnownMessageSequence = 0;
    isLoadingHistory = false;
    chatInitialized = false;

    // Clear active users
    activeUsers = [];

    console.log('Social features disabled, connection state reset');
  }

  // Update the UI to reflect the new state
  updateSocialUI();
}

// Update user name
function updateUserName(name) {
  userName = name;

  // Save to sync storage
  chrome.storage.sync.set({ tiptopUserName: name }, function() {
    if (chrome.runtime.lastError) {
      console.error('Error saving user name to sync storage:', chrome.runtime.lastError);
      // Fall back to local storage
      chrome.storage.local.set({ tiptopUserName: name });
    }
  });

  // Update server if connected
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify({
      type: 'presence_update',
      content: { name: name }
    }));
  }
}

// Simple HTML escaping function
function escapeHtml(unsafe) {
  if (!unsafe) {
    return '';
  }
  // Ensure string conversion in case input is not a string
  const safeString = String(unsafe);
  return safeString
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// Save a chat message as a note for history
function saveMessageAsNote(senderId, senderName, message, timestamp, messageId) {
  // Skip if we're in mock mode and this is a message from the current user
  // This prevents duplicate messages in the chat history
  if (useMockWebSocket && senderId === userId) {
    console.log('Skipping saving own message as note in mock mode to prevent duplicates');
    return;
  }

  // Create a note object from the chat message
  const note = {
    url: window.location.href,
    title: document.title,
    text: message,
    userName: senderName || 'Unknown User',
    timestamp: timestamp || new Date().toISOString(),
    noteId: `chat_${messageId}`,
    userId: senderId,
    type: 'chat' // Mark this as a chat message
  };

  // Create a URL-safe key for the current URL
  const currentUrl = note.url;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const notesKey = `tiptop_notes_${urlKey}`;

  // Store the note in sync storage
  chrome.storage.sync.get([notesKey], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
      // Fall back to local storage
      saveNoteToLocalStorage(note, urlKey);
      return;
    }

    let urlNotes = result[notesKey] || [];

    // Check if we already have this note (by ID)
    const existingNoteIndex = urlNotes.findIndex(n => n.noteId === note.noteId);
    if (existingNoteIndex >= 0) {
      // Update existing note
      urlNotes[existingNoteIndex] = note;
    } else {
      // Add new note
      urlNotes.push(note);

      // Limit to 20 notes per URL for sync storage
      if (urlNotes.length > 20) {
        urlNotes = urlNotes.slice(-20);
      }
    }

    // Save updated notes
    const storageData = {};
    storageData[notesKey] = urlNotes;

    chrome.storage.sync.set(storageData, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving notes to sync storage:', chrome.runtime.lastError);
        // Fall back to local storage
        saveNoteToLocalStorage(note, urlKey);
        return;
      }

      // If we're currently on this URL, refresh the history display
      if (window.location.href === currentUrl) {
        // Dispatch a custom event that content.js can listen for
        document.dispatchEvent(new CustomEvent('tiptop-notes-updated'));
      }
    });
  });
}

// Helper function to save note to local storage as fallback
function saveNoteToLocalStorage(note, urlKey) {
  const notesKey = `tiptop_notes_${urlKey}`;
  const currentUrl = note.url;

  chrome.storage.local.get([notesKey], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing local storage for notes:', chrome.runtime.lastError);
      return;
    }

    let urlNotes = result[notesKey] || [];

    // Check if we already have this note (by ID)
    const existingNoteIndex = urlNotes.findIndex(n => n.noteId === note.noteId);
    if (existingNoteIndex >= 0) {
      // Update existing note
      urlNotes[existingNoteIndex] = note;
    } else {
      // Add new note
      urlNotes.push(note);

      // Limit to 50 notes per URL for local storage
      if (urlNotes.length > 50) {
        urlNotes = urlNotes.slice(-50);
      }
    }

    // Save updated notes
    const storageData = {};
    storageData[notesKey] = urlNotes;

    chrome.storage.local.set(storageData, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving notes to local storage:', chrome.runtime.lastError);
        return;
      }

      // If we're currently on this URL, refresh the history display
      if (window.location.href === currentUrl) {
        // Dispatch a custom event that content.js can listen for
        document.dispatchEvent(new CustomEvent('tiptop-notes-updated'));
      }
    });
  });
}

// Send a note to the server
function sendNote(noteText) {
  if (!socket || socket.readyState !== WebSocket.OPEN) {
    if (useMockWebSocket) {
      // In mock mode, just save the note locally
      const timestamp = new Date().toISOString();
      const noteId = `mock_note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Create a note object
      const note = {
        url: window.location.href,
        title: document.title,
        text: noteText,
        userName: userName || 'Anonymous User',
        timestamp: timestamp,
        noteId: noteId,
        userId: userId,
        type: 'note'
      };

      // Create a URL-safe key for the current URL
      const currentUrl = note.url;
      const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
      const notesKey = `tiptop_notes_${urlKey}`;

      // Store the note in sync storage
      chrome.storage.sync.get([notesKey], function(result) {
        if (chrome.runtime.lastError) {
          console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
          // Fall back to local storage
          saveNoteToLocalStorage(note, urlKey);
          return;
        }

        let urlNotes = result[notesKey] || [];

        // Add new note
        urlNotes.push(note);

        // Limit to 20 notes per URL for sync storage
        if (urlNotes.length > 20) {
          urlNotes = urlNotes.slice(-20);
        }

        // Save updated notes
        const storageData = {};
        storageData[notesKey] = urlNotes;

        chrome.storage.sync.set(storageData, function() {
          if (chrome.runtime.lastError) {
            console.error('Error saving notes to sync storage:', chrome.runtime.lastError);
            // Fall back to local storage
            saveNoteToLocalStorage(note, urlKey);
            return;
          }

          // Dispatch a custom event that content.js can listen for
          document.dispatchEvent(new CustomEvent('tiptop-notes-updated'));
          showNotification('Note saved', 'success');
        });
      });

      return true;
    } else {
      // Save note locally and show notification
      const timestamp = new Date().toISOString();
      const noteId = `offline_note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Create and save the note
      const note = {
        url: window.location.href,
        title: document.title,
        text: noteText,
        userName: userName || 'Anonymous User',
        timestamp: timestamp,
        noteId: noteId,
        userId: userId,
        type: 'note'
      };

      // Save using our helper function
      const urlKey = btoa(note.url).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
      saveNoteToLocalStorage(note, urlKey);

      showNotification('Not connected to server, note saved locally only', 'warning');
      return true;
    }
  }

  // Generate a unique note ID
  const noteId = `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  // Create the note object
  const noteObj = {
    type: 'note',
    content: noteText,
    noteId: noteId,
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString(),
    userName: userName
  };

  // Send note to server
  socket.send(JSON.stringify(noteObj));

  // Also save locally
  saveMessageAsNote(userId, userName, noteText, noteObj.timestamp, noteId);

  return true;
}

// Handle incoming note from server
function handleNoteMessage(data) {
  // Make sure we have all required fields
  if (!data.content || !data.url) {
    console.error('Note message missing required fields:', data);
    return;
  }

  // Create a standardized note object
  const note = {
    url: data.url,
    title: data.title || document.title,
    text: data.content,
    userName: data.userName || 'Unknown User',
    timestamp: data.timestamp || new Date().toISOString(),
    noteId: data.noteId || `server_note_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    userId: data.userId || 'unknown',
    type: 'note'
  };

  // Create a URL-safe key for the note's URL
  const currentUrl = note.url;
  const urlKey = btoa(currentUrl).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  const notesKey = `tiptop_notes_${urlKey}`;

  // Store the note in sync storage
  chrome.storage.sync.get([notesKey], function(result) {
    if (chrome.runtime.lastError) {
      console.error('Error accessing sync storage for notes:', chrome.runtime.lastError);
      // Fall back to local storage
      saveNoteToLocalStorage(note, urlKey);
      return;
    }

    let urlNotes = result[notesKey] || [];

    // Check if we already have this note (by ID)
    const existingNoteIndex = urlNotes.findIndex(n => n.noteId === note.noteId);
    if (existingNoteIndex >= 0) {
      // Update existing note
      urlNotes[existingNoteIndex] = note;
    } else {
      // Add new note
      urlNotes.push(note);

      // Limit to 20 notes per URL for sync storage
      if (urlNotes.length > 20) {
        urlNotes = urlNotes.slice(-20);
      }
    }

    // Save updated notes
    const storageData = {};
    storageData[notesKey] = urlNotes;

    chrome.storage.sync.set(storageData, function() {
      if (chrome.runtime.lastError) {
        console.error('Error saving notes to sync storage:', chrome.runtime.lastError);
        // Fall back to local storage
        saveNoteToLocalStorage(note, urlKey);
        return;
      }

      // If we're currently on this URL, refresh the history display
      if (window.location.href === currentUrl) {
        // Dispatch a custom event that content.js can listen for
        document.dispatchEvent(new CustomEvent('tiptop-notes-updated'));
      }
    });
  });
}

// Last resort function to force scrolling using direct DOM manipulation
function forceScrollToBottom() {
  console.log('🔨 Using direct DOM manipulation to force scroll');

  // Get the chat log element
  const chatLogElement = document.getElementById('tiptop-chat-log');
  if (!chatLogElement) {
    console.error('❌ Cannot force scroll: Chat log element not found');
    return;
  }

  // Try multiple approaches to ensure scrolling works

  // 1. Direct scrollTop manipulation
  chatLogElement.scrollTop = chatLogElement.scrollHeight;

  // 2. Use scrollIntoView on the last message
  const messages = chatLogElement.querySelectorAll('.tiptop-chat-message');
  if (messages && messages.length > 0) {
    const lastMessage = messages[messages.length - 1];
    lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
  }

  // 3. Use a more aggressive approach with JavaScript animation
  // This manually animates the scroll position to ensure it reaches the bottom
  const startPosition = chatLogElement.scrollTop;
  const targetPosition = chatLogElement.scrollHeight;
  const distance = targetPosition - startPosition;

  if (distance > 0) {
    const duration = 100; // ms
    const startTime = performance.now();

    function animateScroll(currentTime) {
      const elapsedTime = currentTime - startTime;
      const progress = Math.min(elapsedTime / duration, 1);

      chatLogElement.scrollTop = startPosition + distance * progress;

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      } else {
        // Final position check
        setTimeout(() => {
          chatLogElement.scrollTop = chatLogElement.scrollHeight;
        }, 50);
      }
    }

    requestAnimationFrame(animateScroll);
  }
}

// Function to force scroll chat to bottom when panel is reopened
function forceScrollChatOnReopen() {
  console.log('🔄 Force scrolling chat on panel reopen');

  // Get the chat log element
  const chatLogElement = document.getElementById('tiptop-chat-log');
  if (!chatLogElement) {
    console.error('❌ Cannot force scroll: Chat log element not found');
    return;
  }

  // Use a series of aggressive scroll attempts with increasing delays
  [0, 50, 100, 200, 500, 1000].forEach(delay => {
    setTimeout(() => {
      if (chatLogElement) {
        // Direct scrollTop manipulation
        chatLogElement.scrollTop = chatLogElement.scrollHeight;
        console.log(`⏱️ ${delay}ms: Direct scroll attempt - scrollTop=${chatLogElement.scrollTop}, scrollHeight=${chatLogElement.scrollHeight}`);

        // Try scrollIntoView on the last message
        const messages = chatLogElement.querySelectorAll('.tiptop-chat-message');
        if (messages && messages.length > 0) {
          const lastMessage = messages[messages.length - 1];
          lastMessage.scrollIntoView({ behavior: 'auto', block: 'end' });
          console.log(`⏱️ ${delay}ms: scrollIntoView on last message`);
        }

        // Reset any user scroll tracking
        userHasScrolledUp = false;
      }
    }, delay);
  });
}

// Function to handle panel reopening
function handlePanelReopen() {
  console.log('🔄 Handling panel reopen');

  // Check if social features are enabled
  if (!socialEnabled) {
    console.log('Social features are disabled, not reconnecting on panel reopen');
    return;
  }

  // First, make sure the chat UI is properly initialized
  initializeChatUI();

  // Check if we already have an active connection
  if (socket && socket.readyState === WebSocket.OPEN) {
    console.log('WebSocket already connected, refreshing UI');

    // Update UI to show connected status
    updateSocialUI();

    // Request users list to refresh the UI
    if (socket.readyState === WebSocket.OPEN) {
      console.log('Requesting users list after panel reopen');
      socket.send(JSON.stringify({
        type: 'get_users',
        url: window.location.href
      }));

      // Also request history messages to ensure chat is populated
      console.log('Requesting history messages after panel reopen');
      socket.send(JSON.stringify({
        type: 'get_history',
        url: window.location.href
      }));
    }

    // Force scroll chat to bottom
    setTimeout(forceScrollChatOnReopen, 100);

    // SIMPLIFIED: Request fresh data from server
    setTimeout(() => {
      refreshChatFromServer();
    }, 200);

    return;
  }

  // If we have a socket but it's not open, clean it up
  if (socket && socket.readyState !== WebSocket.OPEN) {
    console.log('Cleaning up existing socket in state:', socket.readyState);
    try {
      socket.close();
    } catch (e) {
      console.error('Error closing existing socket:', e);
    }
    socket = null;
  }

  // Reset connection state
  isConnected = false;
  reconnectAttempts = 0;

  // Show connecting status immediately
  const statusElement = document.querySelector('.tiptop-collaboration-status');
  if (statusElement) {
    statusElement.style.display = 'block';
    statusElement.innerHTML = '<span class="tiptop-status-connecting">●</span> Connecting...';
  }

  // Connect to WebSocket server
  console.log('Initiating new WebSocket connection after panel reopen');
  connectWebSocket();

  // SIMPLIFIED: Will load from server when connected
  console.log('Will load messages from server when WebSocket connects');

  // Force scroll chat to bottom after a delay
  setTimeout(forceScrollChatOnReopen, 500);
}

// Helper function to ensure chat UI is properly initialized
function initializeChatUI() {
  console.log('Ensuring chat UI is properly initialized');

  // Make sure the chat log exists
  const chatLogElement = document.getElementById('tiptop-chat-log');
  if (!chatLogElement) {
    console.log('Chat log element not found, may need to initialize social UI');
    updateSocialUI();
    return;
  }

  // SIMPLIFIED: If chat log is empty but we have active users, request from server
  if (chatLogElement.children.length === 0 && activeUsers.length > 0) {
    console.log('Chat log is empty but we have active users, requesting from server');
    requestChatHistory();
  }

  // Make sure the chat input is enabled if social features are on
  const chatInput = document.getElementById('tiptop-chat-input');
  if (chatInput && socialEnabled) {
    chatInput.disabled = false;
    chatInput.placeholder = 'Type a message...';
  }
}

// Function to enable inline name editing
function enableInlineNameEdit(nameElement, currentName) {
  // Extract just the name without "(You)"
  const nameOnly = currentName;

  // Make the element editable
  nameElement.contentEditable = true;
  nameElement.classList.add('editing');
  nameElement.textContent = nameOnly; // Remove "(You)" during editing

  // Focus and select all text
  nameElement.focus();
  const range = document.createRange();
  range.selectNodeContents(nameElement);
  const selection = window.getSelection();
  selection.removeAllRanges();
  selection.addRange(range);

  // Handle save on Enter or blur
  function saveEdit() {
    const newName = nameElement.textContent.trim();
    nameElement.contentEditable = false;
    nameElement.classList.remove('editing');

    if (newName && newName !== currentName) {
      // Update the name
      updateUserName(newName);
      // Store the new name
      chrome.storage.local.set({ tiptopUserName: newName });
    }

    // Restore the display with "(You)"
    nameElement.textContent = `${newName || currentName} (You)`;
  }

  // Handle cancel on Escape
  function cancelEdit() {
    nameElement.contentEditable = false;
    nameElement.classList.remove('editing');
    nameElement.textContent = `${currentName} (You)`;
  }

  // Event listeners
  nameElement.addEventListener('blur', saveEdit, { once: true });
  nameElement.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  }, { once: true });
}

// Export functions for use in content.js
window.TipTopSocial = {
  initialize: initializeSocialClient,
  sendChatMessage,
  sendNote,
  toggleSocialFeatures,
  updateUserName,
  isConnected: () => isConnected,
  _getUserId: () => userId, // Expose userId for internal use
  // SIMPLIFIED: Export new functions for testing
  refreshChatFromServer,
  requestChatHistory,
  debugMessages: () => {
    console.log('=== SIMPLIFIED CHAT DEBUG ===');
    console.log(`Displayed message IDs: ${displayedMessageIds.size}`);
    console.log(`Last known sequence: ${lastKnownMessageSequence}`);
    console.log(`Is loading history: ${isLoadingHistory}`);
    console.log(`Chat initialized: ${chatInitialized}`);
    console.log('=== END DEBUG ===');
  }
  _isMockMode: () => useMockWebSocket, // Expose mock mode status for internal use
  forceScrollChatOnReopen, // Add the scroll function to the exported object
  handlePanelReopen, // Add the panel reopen handler to the exported object
  enableInlineNameEdit // Export the inline name editing function
};
