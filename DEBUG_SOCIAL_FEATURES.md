# Debug Social Features - Step by Step

## Issue Summary
- Social features appear disabled by default
- `window.TipTopSocial.debugMessages()` shows "window is not defined" error
- "Who's on the same page?" section shows nothing when enabled

## Fixed Issues
✅ **Syntax Error**: Missing comma in export object - FIXED
✅ **Variable Reference**: `processedMessageIds` → `displayedMessageIds` - FIXED
✅ **Default Setting**: `socialEnabled = false` → `socialEnabled = true` - FIXED

## Debug Steps

### Step 1: Check if Scripts are Loading
Open browser console and run:
```javascript
// Check if scripts are loaded
console.log('Config loaded:', typeof TipTopConfig !== 'undefined');
console.log('Random names loaded:', typeof window.TipTopRandomNames !== 'undefined');
console.log('Social client loaded:', typeof window.TipTopSocial !== 'undefined');
```

**Expected Output:**
```
Config loaded: true
Random names loaded: true
Social client loaded: true
```

### Step 2: Check Social Client Status
```javascript
// Check social client status
if (window.TipTopSocial) {
    console.log('✅ TipTopSocial is available');
    console.log('Functions available:', Object.keys(window.TipTopSocial));
} else {
    console.log('❌ TipTopSocial is NOT available');
}
```

### Step 3: Check Initialization
```javascript
// Check if social client is initialized
if (window.TipTopSocial) {
    try {
        window.TipTopSocial.debugMessages();
    } catch (error) {
        console.error('Error calling debugMessages:', error);
        
        // Try manual initialization
        console.log('Attempting manual initialization...');
        window.TipTopSocial.initialize();
        
        // Wait a bit then try again
        setTimeout(() => {
            try {
                window.TipTopSocial.debugMessages();
            } catch (e) {
                console.error('Still failing after manual init:', e);
            }
        }, 2000);
    }
}
```

### Step 4: Check DOM Elements
```javascript
// Check if social UI elements exist
const socialSection = document.querySelector('[data-section="collaboration"]');
const collaborationDiv = document.getElementById('tiptop-collaboration');
const userList = document.getElementById('tiptop-user-list');
const chatContainer = document.getElementById('tiptop-chat-container');

console.log('Social section toggle:', socialSection ? '✅ Found' : '❌ Missing');
console.log('Collaboration div:', collaborationDiv ? '✅ Found' : '❌ Missing');
console.log('User list:', userList ? '✅ Found' : '❌ Missing');
console.log('Chat container:', chatContainer ? '✅ Found' : '❌ Missing');

if (socialSection) {
    console.log('Social section classes:', socialSection.className);
    console.log('Social section active:', socialSection.classList.contains('active'));
}
```

### Step 5: Check Storage Settings
```javascript
// Check storage settings
chrome.storage.sync.get(['tiptopSocialEnabled'], (result) => {
    console.log('Storage - Social enabled:', result.tiptopSocialEnabled);
});

chrome.storage.local.get(['tiptopSocialEnabled'], (result) => {
    console.log('Local storage - Social enabled:', result.tiptopSocialEnabled);
});
```

### Step 6: Force Enable Social Features
```javascript
// Force enable social features
if (window.TipTopSocial) {
    console.log('🔧 Force enabling social features...');
    
    // Set storage
    chrome.storage.sync.set({tiptopSocialEnabled: true});
    chrome.storage.local.set({tiptopSocialEnabled: true});
    
    // Toggle on
    window.TipTopSocial.toggleSocialFeatures(true);
    
    // Check toggle state
    const socialToggle = document.querySelector('[data-section="collaboration"]');
    if (socialToggle) {
        socialToggle.classList.add('active');
        console.log('✅ Social toggle activated');
    }
    
    // Wait and check status
    setTimeout(() => {
        window.TipTopSocial.debugMessages();
    }, 3000);
}
```

### Step 7: Check Console for Errors
Look for these specific errors in the console:
- ❌ `Uncaught ReferenceError: window is not defined`
- ❌ `Cannot read property 'debugMessages' of undefined`
- ❌ `TipTop social client not available`
- ❌ Any syntax errors in social-client.js

### Step 8: Manual Panel Creation (If Needed)
```javascript
// If panel doesn't exist, try to create it manually
if (!document.getElementById('tiptop-collaboration')) {
    console.log('🔧 Creating social panel manually...');
    
    // Find the main panel
    const mainPanel = document.querySelector('.tiptop-panel-content');
    if (mainPanel) {
        // Add collaboration section
        const collaborationHTML = `
            <div id="tiptop-collaboration" class="tiptop-section">
                <div class="tiptop-collaboration-status">
                    <span class="tiptop-status-connecting">●</span> Connecting...
                </div>
                <div id="tiptop-collaboration-loading" style="display: block;">
                    <p>Connecting to find others viewing this page...</p>
                </div>
                <div class="tiptop-collaboration-controls" style="display: none;">
                    <div id="tiptop-user-list"></div>
                    <div id="tiptop-chat-container">
                        <div class="tiptop-chat-log" id="tiptop-chat-log"></div>
                        <div class="tiptop-chat-input-container">
                            <input type="text" id="tiptop-chat-input" placeholder="Type a message...">
                            <button id="tiptop-chat-send">Send</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        mainPanel.insertAdjacentHTML('beforeend', collaborationHTML);
        console.log('✅ Social panel created manually');
        
        // Try to initialize again
        if (window.TipTopSocial) {
            window.TipTopSocial.initialize();
        }
    }
}
```

## Expected Working State

When everything is working correctly, you should see:

1. **Console Output:**
   ```
   🚀 Initializing TipTop social client
   ✅ Social client initialized with userId: user_xxxxx
   ✅ Social client initialized with userName: [Random Name]
   ✅ Social features enabled: true
   ```

2. **Debug Output:**
   ```
   === SIMPLIFIED CHAT DEBUG ===
   Displayed message IDs: 0
   Last known sequence: 0
   Is loading history: false
   Chat initialized: false
   === END DEBUG ===
   ```

3. **UI Elements:**
   - "Who's on the same page?" section visible and active
   - Shows "Connecting..." status
   - User list shows your name
   - Chat input is enabled

## If Still Not Working

1. **Check Extension Reload**: Try reloading the extension in chrome://extensions/
2. **Check Page Refresh**: Refresh the webpage completely
3. **Check Console Errors**: Look for any JavaScript errors
4. **Check Network**: Ensure WebSocket can connect to server

Run through these steps and let me know what you find!
